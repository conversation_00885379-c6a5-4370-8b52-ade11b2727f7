import 'package:flutter/material.dart';
import 'package:jyt_components_package/theme/colors.dart';
import 'package:jyt_components_package/theme/sizes.dart';

/// 卡片和容器主题
class AppCardTheme {
  /// 亮色卡片主题
  static CardThemeData lightCardTheme() {
    return CardThemeData(
      color: AppColors.background200,
      elevation: 2.0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(AppRadiusSize.radius6)),
      margin: const EdgeInsets.all(8.0),
      clipBehavior: Clip.antiAlias,
    );
  }

  /// 暗色卡片主题
  static CardThemeData darkCardTheme() {
    return CardThemeData(
      color: Colors.grey[800],
      elevation: 2.0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(AppRadiusSize.radius6)),
      margin: const EdgeInsets.all(8.0),
      clipBehavior: Clip.antiAlias,
    );
  }

  /// 容器装饰样式 - 标准
  static BoxDecoration standardDecoration() {
    return BoxDecoration(
      color: AppColors.background100,
      borderRadius: BorderRadius.circular(AppRadiusSize.radius6),
      border: Border.all(color: AppColors.border100),
    );
  }

  /// 容器装饰样式 - 有阴影
  static BoxDecoration shadowDecoration() {
    return BoxDecoration(
      color: AppColors.background100,
      borderRadius: BorderRadius.circular(AppRadiusSize.radius6),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.1),
          blurRadius: 4.0,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  /// 容器装饰样式 - 强调
  static BoxDecoration accentDecoration() {
    return BoxDecoration(
      color: AppColors.primary.withValues(alpha: 0.08),
      borderRadius: BorderRadius.circular(AppRadiusSize.radius6),
      border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
    );
  }
}
