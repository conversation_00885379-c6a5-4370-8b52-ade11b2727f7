import 'package:flutter/material.dart';
import 'package:jyt_components_package/theme/colors.dart';

/// Toast类型
enum ToastType { success, error, warning, info }

/// Toast组件
class AppToast extends StatelessWidget {
  final String message;
  final ToastType type;
  final Duration duration;

  // 颜色常量
  static const Map<ToastType, Color> _backgroundColors = {
    ToastType.success: Color(0xFFF0F9EB),
    ToastType.error: Color(0xFFFEF0F0),
    ToastType.warning: Color(0xFFFDF6EC),
    ToastType.info: Color(0xFFEDF2FC),
  };

  static const Map<ToastType, Color> _borderColors = {
    ToastType.success: Color(0xFFE1F3D8),
    ToastType.error: Color(0xFFFDE2E2),
    ToastType.warning: Color(0xFFFAECD8),
    ToastType.info: Color(0xFFDCE5F9),
  };

  static final Map<ToastType, Color> _contentColors = {
    ToastType.success: AppColors.success,
    ToastType.error: AppColors.error,
    ToastType.warning: AppColors.warning,
    ToastType.info: AppColors.info,
  };

  static const Map<ToastType, IconData> _icons = {
    ToastType.success: Icons.check_circle,
    ToastType.error: Icons.error,
    ToastType.warning: Icons.warning,
    ToastType.info: Icons.info,
  };

  const AppToast({
    super.key,
    required this.message,
    this.type = ToastType.info,
    this.duration = const Duration(seconds: 3),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: _backgroundColors[type],
        borderRadius: BorderRadius.circular(4),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            spreadRadius: 1,
            offset: const Offset(0, 3),
          ),
        ],
        border: Border.all(color: _borderColors[type]!, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(_icons[type], color: _contentColors[type], size: 18),
          const SizedBox(width: 10),
          Flexible(
            child: Text(
              message,
              style: TextStyle(
                color: _contentColors[type],
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
