import 'dart:async';
import 'package:flutter/material.dart';
import 'package:jyt_components_package/components/feedback/loading/app_loading.dart';

/// Loading管理器
///
/// 提供全局加载指示器的显示和隐藏功能，支持多种使用场景:
/// 1. 简单的全屏加载: LoadingManager.show() / LoadingManager.hide()
/// 2. 带文本的加载: LoadingManager.show('加载中...')
/// 3. 自动隐藏的加载: LoadingManager.show('加载中...', duration: Duration(seconds: 5))
/// 4. 异步任务包装器: LoadingManager.showDuring(() => yourAsyncTask())
/// 5. 页面局部加载: 直接使用AppLoading组件
class LoadingManager {
  static final LoadingManager instance = LoadingManager._();
  LoadingManager._();

  // 成员变量
  OverlayEntry? _overlayEntry;
  Timer? _timer;
  int _loadingCounter = 0; // 加载计数器，处理嵌套调用

  // 全局变量
  static late GlobalKey<NavigatorState> _navigatorKey;

  /// 初始化全局context
  static void init(GlobalKey<NavigatorState> navigatorKey) {
    _navigatorKey = navigatorKey;
  }

  /// 获取OverlayState
  OverlayState? get _overlay => _navigatorKey.currentState?.overlay;

  /// 显示Loading
  void show({
    String? text,
    Duration? duration,
    Color? color,
    Color? backgroundColor,
    double? backgroundOpacity,
  }) {
    final overlay = _overlay;
    if (overlay == null) {
      debugPrint('Loading错误: 无法获取Overlay');
      return;
    }

    // 计数器增加
    _loadingCounter++;
    if (_loadingCounter > 1) {
      // 如果已经有Loading在显示，不再创建新的
      return;
    }

    try {
      // 创建新的OverlayEntry
      _overlayEntry = OverlayEntry(
        builder: (context) {
          // 根据主题配置遮罩颜色和透明度
          final bool isDarkMode = Theme.of(context).brightness == Brightness.dark;
          final Color bgColor = backgroundColor ?? (isDarkMode ? Colors.black : Colors.white);
          final double opacity = backgroundOpacity ?? (isDarkMode ? 0.8 : 0.9);

          return Stack(
            children: [
              // 全屏半透明遮罩
              ModalBarrier(color: bgColor.withValues(alpha: opacity), dismissible: false),
              // 原有加载指示器
              Center(
                child: AppLoading(
                  isLoading: true,
                  text: text,
                  color: color,
                  backgroundColor: Colors.transparent, // 背景透明
                  backgroundOpacity: 0,
                ),
              ),
            ],
          );
        },
      );

      // 将OverlayEntry添加到Overlay
      overlay.insert(_overlayEntry!);

      // 如果设置了持续时间，则自动隐藏
      if (duration != null) {
        _timer = Timer(duration, hide);
      }
    } catch (e) {
      debugPrint('Loading显示错误: $e');
      hide();
    }
  }

  /// 隐藏Loading
  void hide() {
    // 计数器减少
    if (_loadingCounter > 0) {
      _loadingCounter--;
    }

    // 如果计数器不为0，说明还有其他地方在使用Loading，不关闭
    if (_loadingCounter > 0) {
      return;
    }

    _timer?.cancel();
    _timer = null;
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
    }
  }

  /// 在异步任务期间显示Loading
  ///
  /// 在异步任务执行期间显示Loading，任务完成后自动隐藏
  /// 返回异步任务的结果
  Future<T> showDuring<T>(
    Future<T> Function() asyncTask, {
    String? text,
    Color? color,
    Color? backgroundColor,
    double? backgroundOpacity,
  }) async {
    try {
      show(
        text: text,
        color: color,
        backgroundColor: backgroundColor,
        backgroundOpacity: backgroundOpacity,
      );
      final result = await asyncTask();
      return result;
    } finally {
      hide();
    }
  }

  /* 静态方法部分 */

  /// 显示Loading的静态方法
  static void showLoading({
    String? text,
    Duration? duration,
    Color? color,
    Color? backgroundColor,
    double? backgroundOpacity,
  }) {
    instance.show(
      text: text,
      duration: duration,
      color: color,
      backgroundColor: backgroundColor,
      backgroundOpacity: backgroundOpacity,
    );
  }

  /// 隐藏Loading的静态方法
  static void hideLoading() {
    instance.hide();
  }

  /// 在异步任务期间显示Loading的静态方法
  static Future<T> during<T>(
    Future<T> Function() asyncTask, {
    String? text,
    Color? color,
    Color? backgroundColor,
    double? backgroundOpacity,
  }) async {
    return instance.showDuring(
      asyncTask,
      text: text,
      color: color,
      backgroundColor: backgroundColor,
      backgroundOpacity: backgroundOpacity,
    );
  }
}
