import 'package:flutter/material.dart';
import 'package:jyt_components_package/theme/colors.dart';
import 'package:jyt_components_package/theme/sizes.dart';

/// 按钮类型枚举
enum ButtonType {
  primary, // 主要按钮
  success, // 成功按钮
  warning, // 警告按钮
  danger, // 危险按钮
  info, // 信息按钮
  default_, // 默认按钮
  transparent, // 透明按钮
}

/// 按钮尺寸枚举
enum ButtonSize {
  large, // 大型按钮
  medium, // 中型按钮
  small, // 小型按钮
  mini, // 迷你按钮
}

class AppButton extends StatefulWidget {
  /// 按钮文本
  final String text;

  /// 图标数据
  final IconData? iconData;

  /// 图标颜色
  final Color? color;

  /// 自定义背景颜色（优先级高于type）
  final Color? backgroundColor;

  /// 点击回调(如果为空没有点击效果)
  final VoidCallback? onPressed;

  /// 按钮类型
  final ButtonType type;

  /// 按钮尺寸
  final ButtonSize size;

  /// 是否为圆角按钮
  final bool round;

  /// 是否为圆形按钮
  final bool circle;

  /// 是否禁用
  final bool disabled;

  /// 是否加载中
  final bool loading;

  /// 是否占满父容器宽度
  final bool expand;

  /// 是否为文字按钮
  final bool textOnly;

  const AppButton({
    super.key,
    this.text = '',
    this.iconData,
    this.color,
    this.backgroundColor,
    this.onPressed,
    this.type = ButtonType.default_,
    this.size = ButtonSize.medium,
    this.round = false,
    this.circle = false,
    this.disabled = false,
    this.loading = false,
    this.expand = false,
    this.textOnly = false,
  });

  @override
  State<AppButton> createState() => _AppButtonState();
}

class _AppButtonState extends State<AppButton> {
  bool _isHovered = false;
  bool _isPressed = false;

  /// 只显示文字
  bool get onlyShowText => widget.text.isNotEmpty && !hasIcon;

  /// 只显示图标
  bool get onlyShowIcon => hasIcon && widget.text.isEmpty;

  /// 是否有图标
  bool get hasIcon {
    // 文字按钮不考虑loading状态
    if (widget.textOnly) return widget.iconData != null;
    return widget.iconData != null || widget.loading;
  }

  /// 是否处于加载状态
  bool get isLoading {
    // 文字按钮不显示loading效果
    if (widget.textOnly) return false;
    return widget.loading;
  }

  /// 获取按钮高度
  double get buttonHeight {
    switch (widget.size) {
      case ButtonSize.large:
        return 44;
      case ButtonSize.medium:
        return 32;
      case ButtonSize.small:
        return 24;
      case ButtonSize.mini:
        return 20;
    }
  }

  /// 获取字体大小
  double get fontSize {
    switch (widget.size) {
      case ButtonSize.large:
        return 16;
      case ButtonSize.medium:
        return 14;
      case ButtonSize.small:
        return 12;
      case ButtonSize.mini:
        return 12;
    }
  }

  /// 获取图标大小
  double get iconSize {
    switch (widget.size) {
      case ButtonSize.large:
        return AppIconSize.large;
      case ButtonSize.medium:
        return AppIconSize.medium;
      case ButtonSize.small:
        return AppIconSize.small;
      case ButtonSize.mini:
        return AppIconSize.small;
    }
  }

  /// 按钮内边距
  EdgeInsets get padding {
    // 根据尺寸获取水平内边距
    double horizontal;

    switch (widget.size) {
      case ButtonSize.large:
        horizontal = 20;
        break;
      case ButtonSize.medium:
        horizontal = 12;
        break;
      case ButtonSize.small:
        horizontal = 8;
        break;
      case ButtonSize.mini:
        horizontal = 4;
        break;
    }

    // 圆形按钮特殊处理
    if (widget.circle) return EdgeInsets.zero;

    // 只有图标的情况
    if (onlyShowIcon) return EdgeInsets.all(horizontal / 2);

    // 文字按钮内边距更小
    if (widget.textOnly)
      return EdgeInsets.symmetric(horizontal: horizontal / 2);

    return EdgeInsets.symmetric(horizontal: horizontal);
  }

  /// 获取按钮圆角
  BorderRadius get borderRadius {
    // 圆形按钮或圆角按钮
    if (widget.circle || widget.round)
      return BorderRadius.circular(buttonHeight / 2);

    // 只有图标但非圆形按钮，使用4px圆角
    if (onlyShowIcon && !widget.circle)
      return BorderRadius.circular(AppRadiusSize.radius4);

    // 默认圆角基于尺寸
    double radius;
    if (widget.size == ButtonSize.small || widget.size == ButtonSize.mini) {
      radius = AppRadiusSize.radius2;
    } else {
      radius = AppRadiusSize.radius4;
    }

    return BorderRadius.circular(radius);
  }

  /// 获取按钮背景色
  Color getBackgroundColor(BuildContext context) {
    // 自定义背景色优先
    if (widget.backgroundColor != null) return widget.backgroundColor!;

    // 文字按钮无背景
    if (widget.textOnly) return Colors.transparent;

    // 颜色映射表
    final Map<ButtonType, Color> colorMap = {
      ButtonType.primary: AppColors.primary,
      ButtonType.success: AppColors.success,
      ButtonType.warning: AppColors.warning,
      ButtonType.danger: AppColors.error,
      ButtonType.info: AppColors.info,
      ButtonType.default_: context.background300,
      ButtonType.transparent: Colors.transparent,
    };

    // 禁用状态
    if (widget.disabled) {
      if (widget.type == ButtonType.primary) return AppColors.primaryDisabled;
      if (widget.type == ButtonType.default_)
        return context.background300.withValues(alpha: .5);
      return colorMap[widget.type]!.withValues(alpha: .5);
    }

    return colorMap[widget.type]!;
  }

  /// 获取文字/图标颜色
  Color getTextColor(BuildContext context, Color bgColor) {
    // 颜色映射表
    final Map<ButtonType, Color> colorMap = {
      ButtonType.primary: AppColors.primary,
      ButtonType.success: AppColors.success,
      ButtonType.warning: AppColors.warning,
      ButtonType.danger: AppColors.error,
      ButtonType.info: AppColors.info,
      ButtonType.default_: context.textPrimary,
    };

    // 禁用状态处理
    if (widget.disabled) {
      // 文字按钮禁用状态
      if (widget.textOnly) return AppColors.textDisabled;

      // 禁用状态的primary按钮文字颜色为白色
      if (widget.type == ButtonType.primary) return AppColors.textWhite;
    }

    // 文字按钮
    if (widget.textOnly) {
      Color baseColor = colorMap[widget.type]!;
      // 悬浮或按下状态，调整颜色
      if (_isPressed) {
        return baseColor.withValues(alpha: .7); // 按下时颜色变淡
      } else if (_isHovered) {
        return baseColor; // 悬浮时保持原色
      }
      return baseColor.withValues(alpha: .85); // 默认状态稍微透明
    }

    // 计算背景色亮度，亮色背景用深色文字，深色背景用亮色文字
    final double luminance = bgColor.computeLuminance();
    return luminance > 0.5 ? AppColors.textSecondary : AppColors.textWhite;
  }

  /// 获取边框样式
  BorderSide? getBorderSide(BuildContext context, Color bgColor) {
    // 文字按钮无边框
    if (widget.textOnly) return null;

    // 默认按钮处理
    if (widget.type == ButtonType.default_) {
      // 默认按钮禁用时无边框
      if (widget.disabled) return null;
      return BorderSide(color: context.border300, width: 1);
    }

    // 其他按钮类型无边框
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final Color bgColor = getBackgroundColor(context);
    final Color textColor = getTextColor(context, bgColor);
    final BorderSide? borderSide = getBorderSide(context, bgColor);

    // 准备按钮内容元素
    List<Widget> contentChildren = [];

    // 构建图标
    final Widget? icon = hasIcon
        ? _buildIcon(widget.color ?? textColor, context)
        : null;

    // 构建文本
    final Widget? textWidget = widget.text.isNotEmpty
        ? Padding(
            padding: const EdgeInsets.only(bottom: 2), // 解决文字垂直对不齐问题
            child: Text(
              widget.text,
              style: TextStyle(
                color: textColor,
                fontSize: fontSize,
                height: 1.0,
              ),
            ),
          )
        : null;

    // 添加元素 - 图标始终在左侧
    if (icon != null) contentChildren.add(icon);

    if (icon != null && textWidget != null)
      contentChildren.add(SizedBox(width: 5));

    if (textWidget != null) contentChildren.add(textWidget);

    // 按钮内容容器
    Widget buttonContent = Container(
      height: buttonHeight,
      padding: padding,
      child: Center(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: contentChildren,
        ),
      ),
    );

    // 应用约束条件
    Widget constrainedButton;
    if (widget.expand) {
      constrainedButton = buttonContent;
    } else if (widget.circle) {
      constrainedButton = SizedBox(
        width: buttonHeight,
        height: buttonHeight,
        child: buttonContent,
      );
    } else if (onlyShowIcon) {
      constrainedButton = SizedBox(width: buttonHeight, child: buttonContent);
    } else {
      constrainedButton = IntrinsicWidth(child: buttonContent);
    }

    // 文字按钮使用不同的构建方式，避免水波纹效果
    if (widget.textOnly) {
      return MouseRegion(
        cursor: (widget.disabled)
            ? SystemMouseCursors.forbidden
            : SystemMouseCursors.click,
        onEnter: (_) {
          if (!widget.disabled) {
            setState(() => _isHovered = true);
          }
        },
        onExit: (_) {
          setState(() => _isHovered = false);
        },
        child: GestureDetector(
          onTapDown: (_) {
            if (!widget.disabled) {
              setState(() => _isPressed = true);
            }
          },
          onTapUp: (_) {
            setState(() => _isPressed = false);
          },
          onTapCancel: () {
            setState(() => _isPressed = false);
          },
          onTap: (widget.disabled) ? null : widget.onPressed,
          child: AnimatedOpacity(
            duration: Duration(milliseconds: 150),
            opacity: _isPressed ? 0.7 : 1.0,
            child: constrainedButton,
          ),
        ),
      );
    }

    // 常规按钮使用原有构建方式
    return ClipRRect(
      borderRadius: borderRadius,
      child: Material(
        color: bgColor,
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius,
          side: borderSide ?? BorderSide.none,
        ),
        child: InkWell(
          borderRadius: borderRadius,
          onTap: (widget.disabled || widget.loading) ? null : widget.onPressed,
          mouseCursor: (widget.disabled || widget.loading)
              ? SystemMouseCursors.forbidden
              : SystemMouseCursors.click,
          child: constrainedButton,
        ),
      ),
    );
  }

  /// 构建图标部分
  Widget _buildIcon(Color textColor, BuildContext context) {
    // 文字按钮不显示loading图标
    if (isLoading) {
      return SizedBox(
        width: iconSize,
        height: iconSize,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(textColor),
        ),
      );
    }

    if (widget.iconData != null)
      return Icon(widget.iconData, color: textColor, size: iconSize);

    return SizedBox();
  }
}
