import 'package:flutter/material.dart';
import 'package:jyt_components_package/theme/colors.dart';

/// App加载组件
///
/// 提供一个通用的加载指示器组件，支持覆盖在子组件上方显示加载状态。
/// 支持自定义颜色、背景色、文字描述等。
class AppLoading extends StatelessWidget {
  /// 是否显示加载中
  final bool isLoading;

  /// 子组件
  final Widget? child;

  /// 加载器颜色
  final Color? color;

  /// 背景颜色
  final Color? backgroundColor;

  /// 背景透明度 (0.0-1.0)
  final double? backgroundOpacity;

  /// 描述文字
  final String? text;

  /// 文字颜色
  final Color? textColor;

  /// 文字大小
  final double? textSize;

  /// 加载内容与文字间距
  final double spacing;

  /// 是否显示背景蒙层
  final bool showBackground;

  /// 蒙层点击事件处理
  final VoidCallback? onBackgroundTap;

  /// 内边距
  final EdgeInsets padding;

  const AppLoading({
    super.key,
    this.isLoading = true,
    this.child,
    this.color,
    this.backgroundColor,
    this.backgroundOpacity,
    this.text,
    this.textColor,
    this.textSize,
    this.spacing = 12.0,
    this.showBackground = true,
    this.onBackgroundTap,
    this.padding = EdgeInsets.zero,
  });

  @override
  Widget build(BuildContext context) {
    // 如果不需要加载，直接显示子组件
    if (!isLoading) {
      return child != null ? Padding(padding: padding, child: child!) : const SizedBox.shrink();
    }

    // 如果没有子组件，只显示加载指示器
    if (child == null) {
      return Center(child: _buildLoadingContent(context));
    }

    // 根据主题配置遮罩颜色和透明度
    final bool isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final Color bgColor = backgroundColor ?? (isDarkMode ? Colors.black : Colors.white);
    final double opacity = backgroundOpacity ?? (isDarkMode ? 0.8 : 0.9);

    // 有子组件，在子组件上方显示加载指示器
    return Stack(
      children: [
        Padding(padding: padding, child: child!), // 底层内容
        // 加载层
        Positioned.fill(
          child:
              showBackground
                  ? GestureDetector(
                    onTap: onBackgroundTap,
                    child: Container(
                      color: bgColor.withValues(alpha: opacity),
                      child: Center(child: _buildLoadingContent(context)),
                    ),
                  )
                  : Center(child: _buildLoadingContent(context)),
        ),
      ],
    );
  }

  /// 构建加载内容
  Widget _buildLoadingContent(BuildContext context) {
    // 获取适合当前主题的颜色
    final Color loadingColor = color ?? Theme.of(context).primaryColor;
    final Color loadingTextColor = textColor ?? AppColors.primary;

    // 使用固定尺寸30x30和固定粗细3.0
    const double loadingSize = 30.0;
    const double strokeWidth = 2.0;

    // 构建加载指示器
    Widget loadingIndicator = _buildLoadingIndicator(loadingColor, loadingSize, strokeWidth);

    // 如果有文字，添加文字
    if (text != null && text!.isNotEmpty) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          loadingIndicator,
          SizedBox(height: spacing),
          Text(
            text!,
            style: TextStyle(
              color: loadingTextColor,
              fontSize: textSize ?? 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      );
    } else {
      return loadingIndicator;
    }
  }

  /// 构建加载指示器
  Widget _buildLoadingIndicator(Color color, double size, double strokeWidth) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(color),
        strokeWidth: strokeWidth,
      ),
    );
  }
}
