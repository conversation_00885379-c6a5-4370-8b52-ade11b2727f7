import 'package:flutter/material.dart';
import 'package:jyt_components_package/components/others/app_custom_tooltip.dart';
import 'dart:math' as math;

import 'package:jyt_components_package/theme/colors.dart';
import 'package:jyt_components_package/theme/sizes.dart';

/// 按钮类型枚举
enum DropdownType {
  primary, // 主要按钮
  success, // 成功按钮
  warning, // 警告按钮
  danger, // 危险按钮
  info, // 信息按钮
  default_, // 默认按钮
  transparent, // 透明按钮
}

/// 下拉菜单位置
enum DropdownPlacement {
  topLeft, // 左上方
  topCenter, // 顶部居中
  topRight, // 右上方
  rightTop, // 右上方
  rightCenter, // 右侧居中
  rightBottom, // 右下方
  bottomLeft, // 左下方
  bottomCenter, // 底部居中
  bottomRight, // 右下方
  leftTop, // 左上方
  leftCenter, // 左侧居中
  leftBottom, // 左下方
}

/// 按钮尺寸枚举
enum DropdownSize {
  large, // 大型按钮
  medium, // 中型按钮
  small, // 小型按钮
  mini, // 迷你按钮
}

/// 下拉菜单触发方式
enum DropdownTrigger {
  hover, // 悬停触发
  click, // 点击触发
}

// /// 下拉菜单位置
// enum DropdownPosition {
//   bottomLeft, // 左下方
//   bottomRight, // 右下方
//   bottomCenter, // 底部居中
//   topLeft, // 左上方
//   topRight, // 右上方
//   topCenter, // 顶部居中
// }

/// 下拉菜单项数据
class DropdownItem {
  /// 显示文本
  final String text;

  /// 值
  final dynamic value;

  /// 图标数据（flutter图标库图标）
  final IconData? iconData;

  /// 是否禁用
  final bool disabled;

  /// 是否在此项下方显示分割线
  final bool divided;

  DropdownItem({
    required this.text,
    required this.value,
    this.iconData,
    this.disabled = false,
    this.divided = false,
  });
}

typedef DropdownItemCallback = void Function(DropdownItem item);

class AppDropdown extends StatefulWidget {
  /// 文本数据
  final String text;

  /// 图标数据（flutter图标库图标）
  final IconData? iconData;

  /// 图标颜色
  final Color? color;

  /// 按钮尺寸
  final DropdownSize size;

  /// 是否禁用
  final bool disabled;

  /// 是否为文字按钮
  final bool textOnly;

  /// 是否占满父容器宽度
  final bool expand;

  /// 按钮类型
  final DropdownType type;

  /// 下拉项列表
  final List<DropdownItem> items;

  /// 下拉菜单宽度，默认与按钮同宽
  final double? dropdownWidth;

  /// 下拉菜单最大高度
  final double maxDropdownHeight;

  final DropdownPlacement placement;

  /// 下拉项选择回调
  final DropdownItemCallback? onItemSelected;

  /// 下拉菜单触发方式
  final DropdownTrigger trigger;

  /// 当前选中项的值
  final dynamic value;

  /// 点击回调(如果为空没有点击效果)
  final VoidCallback? onPressed;

  Widget? child;

  AppDropdown({
    super.key,
    this.text = '',
    this.items = const [],
    this.dropdownWidth,
    this.maxDropdownHeight = 300,
    this.placement = DropdownPlacement.bottomLeft,
    this.onItemSelected,
    this.iconData = Icons.keyboard_arrow_down,
    this.color,
    this.value,
    this.onPressed,
    this.size = DropdownSize.medium,
    this.disabled = false,
    this.trigger = DropdownTrigger.click,
    this.textOnly = false,
    this.expand = false,
    this.type = DropdownType.default_,
    this.child,
  });

  @override
  State<AppDropdown> createState() => _AppDropdownState();
}

class _AppDropdownState extends State<AppDropdown> {
  AppCustomTooltipController controller = AppCustomTooltipController();
  bool _isPressed = false;
  bool _isHovered = false;

  // 缓存计算的尺寸和样式
  late final double _buttonHeight = _getButtonHeight();
  late final double _fontSize = _getFontSize();
  late final double _iconSize = _getIconSize();
  late final double _contentPadding = _getContentPadding();
  late final double _contentVerticalPadding = _getContentVerticalPadding();

  /// 计算文本宽度
  double _getTextWidth(String text, TextStyle style, {int maxLines = 1}) {
    final TextPainter textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      textDirection: TextDirection.ltr,
      maxLines: maxLines,
    );
    textPainter.layout(maxWidth: double.infinity);
    return textPainter.width;
  }

  /// 计算下拉菜单需要的宽度
  double _calculateDropdownWidth(BuildContext context) {
    if (widget.items.isEmpty) {
      return widget.dropdownWidth ?? 200;
    }

    // 最小宽度：使用传入的dropdownWidth或默认值
    double minWidth = widget.dropdownWidth ?? 80;
    // 最大宽度：设置一个合理的最大值，避免下拉菜单过宽
    double maxWidth = 350;

    // 遍历所有项目，计算每个项目所需的宽度
    double maxItemWidth = 0;

    for (var item in widget.items) {
      double itemWidth = 0;

      // 1. 计算文本宽度
      final TextStyle textStyle = TextStyle(
        fontSize: _fontSize,
        fontWeight: FontWeight.normal,
        color: item.disabled ? AppColors.textDisabled : context.textPrimary,
      );
      final textWidth = _getTextWidth(item.text, textStyle);
      itemWidth += textWidth;

      // 2. 计算图标宽度（如果有）
      if (item.iconData != null) {
        itemWidth += _iconSize; // 图标宽度
        itemWidth += 8.0; // 图标与文本之间的间距
      }

      // 3. 添加左右内边距
      itemWidth += _contentPadding * 2;

      // 4. 添加安全边距，避免文本省略号出现
      itemWidth += 10;

      // 5. 更新最大宽度
      maxItemWidth = math.max(maxItemWidth, itemWidth);
    }

    // 确保最终宽度在最小和最大宽度范围内
    return math.min(math.max(maxItemWidth, minWidth), maxWidth);
  }

  /// 强制关闭下拉面板，无论何种模式下
  void _forceCloseDropdown() {
    // 使用 forceHideTooltip 方法强制关闭提示框，无论何种模式
    controller.forceHideTooltip();

    // 强制更新状态
    setState(() {
      _isHovered = false;
    });
  }

  /// 处理传入的 child，添加点击事件以触发下拉菜单显示
  Widget _wrapChild(Widget child) {
    // 如果组件被禁用，直接返回原始 child
    if (widget.disabled) {
      return Opacity(
        opacity: 0.7, // 轻微降低透明度以指示禁用状态
        child: child,
      );
    }

    // 使用 Listener 捕获点击事件，这种方式比 GestureDetector 更底层
    // 确保无论 child 是什么组件，都能捕获到点击事件
    return TapRegion(
      onTapInside: (_) {
        controller.showTooltip();
        // setState(() {
        //   isShowTooltip1 = !isShowTooltip1;
        //   print('11111----$isShowTooltip1');
        // });
      },
      child: widget.child,
    );
  }

  /// 获取按钮背景色
  Color getBackgroundColor(BuildContext context) {
    // 文字按钮无背景
    if (widget.textOnly) return Colors.transparent;

    // 颜色映射表
    final colorMap = _getButtonColorMap(context);

    // 禁用状态
    if (widget.disabled) {
      if (widget.type == DropdownType.primary) {
        return AppColors.primaryDisabled;
      } else if (widget.type == DropdownType.default_) {
        return context.background300.withValues(alpha: .5);
      } else if (widget.type == DropdownType.transparent) {
        return context.background300.withValues(alpha: .5);
      }
      return colorMap[widget.type]!.withValues(alpha: .5);
    }

    return colorMap[widget.type]!;
  }

  /// 获取颜色映射表
  Map<DropdownType, Color> _getButtonColorMap(BuildContext context) {
    return {
      DropdownType.primary: AppColors.primary,
      DropdownType.success: AppColors.success,
      DropdownType.warning: AppColors.warning,
      DropdownType.danger: AppColors.error,
      DropdownType.info: AppColors.info,
      DropdownType.default_: context.background300,
      DropdownType.transparent: Colors.transparent,
    };
  }

  /// 获取文本颜色映射表
  Map<DropdownType, Color> _getTextColorMap(BuildContext context) {
    return {
      DropdownType.primary: AppColors.primary,
      DropdownType.success: AppColors.success,
      DropdownType.warning: AppColors.warning,
      DropdownType.danger: AppColors.error,
      DropdownType.info: AppColors.info,
      DropdownType.default_: context.textSecondary,
      DropdownType.transparent: context.textSecondary,
    };
  }

  /// 获取文字/图标颜色
  Color getTextColor(BuildContext context, Color bgColor) {
    // 颜色映射表
    final colorMap = _getTextColorMap(context);

    // 禁用状态处理
    if (widget.disabled) {
      // 文字按钮禁用状态
      if (widget.textOnly) return AppColors.textDisabled;

      // 禁用状态的primary按钮文字颜色为白色
      if (widget.type == DropdownType.primary) return AppColors.textWhite;
      return AppColors.textDisabled;
    }

    // 文字按钮
    if (widget.textOnly) {
      Color baseColor = colorMap[widget.type]!;
      // 悬浮或按下状态，调整颜色
      if (_isPressed) {
        return baseColor.withValues(alpha: .7); // 按下时颜色变淡
      } else if (_isHovered || controller.isShowTooltip) {
        return baseColor; // 悬浮或打开下拉菜单时保持原色
      }
      return baseColor.withValues(alpha: .85); // 默认状态稍微透明
    }

    // 对于透明类型的按钮，直接使用文本次要颜色，不考虑亮度计算
    if (widget.type == DropdownType.transparent) {
      // 使用widget.color优先，否则使用次要文本颜色
      return widget.color ?? context.textSecondary;
    }

    // 计算背景色亮度，亮色背景用深色文字，深色背景用亮色文字
    final double luminance = bgColor.computeLuminance();
    return luminance > 0.5 ? AppColors.textSecondary : AppColors.textWhite;
  }

  @override
  Widget build(BuildContext context) {
    final Color bgColor = getBackgroundColor(context);
    final Color textColor = getTextColor(context, bgColor);
    var borderRadius = BorderRadius.circular(AppRadiusSize.radius4);

    // 准备按钮内容元素
    List<Widget> contentChildren = [];

    // 构建图标
    final Widget? icon = hasIcon
        ? _buildIcon(widget.color ?? textColor, context)
        : null;

    // 构建文本
    final Widget? textWidget = widget.text.isNotEmpty
        ? Padding(
            padding: const EdgeInsets.only(bottom: 2), // 解决文字垂直对不齐问题
            child: Text(
              widget.text,
              style: TextStyle(
                color: textColor,
                fontSize: _fontSize,
                height: 1.0,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          )
        : null;

    if (textWidget != null) contentChildren.add(textWidget);
    if (icon != null && textWidget != null) {
      contentChildren.add(const SizedBox(width: 5));
    }

    if (icon != null) contentChildren.add(icon);

    // 按钮内容容器
    Widget buttonContent = Container(
      height: _buttonHeight,
      padding: _padding,
      child: Center(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: contentChildren,
        ),
      ),
    );

    // 应用约束条件
    Widget constrainedButton;
    if (widget.expand) {
      constrainedButton = buttonContent;
    } else if (onlyShowIcon) {
      constrainedButton = SizedBox(width: _buttonHeight, child: buttonContent);
    } else {
      constrainedButton = IntrinsicWidth(child: buttonContent);
    }

    // 根据 textOnly 属性选择按钮构建方式
    Widget buttonWidget = widget.textOnly
        ? _buildTextButton(constrainedButton, textColor)
        : _buildStandardButton(constrainedButton, bgColor, borderRadius);

    // 处理 child 参数，如果提供了 child，则使用 _wrapChild 方法包装它
    Widget finalChild = widget.child != null
        ? _wrapChild(widget.child!)
        : buttonWidget;

    // 计算下拉菜单宽度
    final double dropdownWidth = _calculateDropdownWidth(context);

    // 可访问性支持
    return Semantics(
      button: true,
      enabled: !widget.disabled,
      label: widget.text.isNotEmpty ? widget.text : '下拉菜单',
      child: AppCustomTooltip(
        arrowColor: context.background300,
        controller: controller,
        placement: transPlacement(widget.placement),
        manual: widget.trigger == DropdownTrigger.click,
        closeOnOutsideClick: widget.trigger == DropdownTrigger.click,
        onVisibleChange: (isVisible) {
          // 状态变化时触发重新渲染
          setState(() {});
        },
        contentChild: Container(
          width: dropdownWidth, // 使用计算的宽度
          child: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
            clipBehavior: Clip.antiAlias,
            color: context.background300,
            child: ConstrainedBox(
              constraints: BoxConstraints(maxHeight: widget.maxDropdownHeight),
              child: NotificationListener<ScrollNotification>(
                onNotification: (_) => true, // 阻止滚动通知冒泡
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    vertical: _contentVerticalPadding,
                  ),
                  child: ListView.builder(
                    padding: EdgeInsets.symmetric(vertical: 4),
                    shrinkWrap: true,
                    itemCount: widget.items.length,
                    itemBuilder: (context, index) {
                      final item = widget.items[index];
                      final isSelected = item.value == widget.value;
                      return _buildDropdownItem(item, isSelected, controller);
                    },
                  ),
                ),
              ),
            ),
          ),
        ),
        child: finalChild,
      ),
    );
  }

  /// 构建图标部分
  Widget _buildIcon(Color textColor, BuildContext context) {
    if (widget.iconData != null)
      return Icon(widget.iconData, color: textColor, size: _iconSize);

    return SizedBox();
  }

  /// 获取按钮高度
  double _getButtonHeight() {
    switch (widget.size) {
      case DropdownSize.large:
        return 44;
      case DropdownSize.medium:
        return 32;
      case DropdownSize.small:
        return 24;
      case DropdownSize.mini:
        return 20;
    }
  }

  /// 获取字体大小
  double _getFontSize() {
    switch (widget.size) {
      case DropdownSize.large:
        return 16;
      case DropdownSize.medium:
        return 14;
      case DropdownSize.small:
        return 12;
      case DropdownSize.mini:
        return 12;
    }
  }

  /// 获取图标大小
  double _getIconSize() {
    switch (widget.size) {
      case DropdownSize.large:
        return AppIconSize.large;
      case DropdownSize.medium:
        return AppIconSize.medium;
      case DropdownSize.small:
        return AppIconSize.small;
      case DropdownSize.mini:
        return AppIconSize.small;
    }
  }

  double _getContentPadding() {
    switch (widget.size) {
      case DropdownSize.large:
        return 12;
      case DropdownSize.medium:
        return 10;
      case DropdownSize.small:
        return 8;
      case DropdownSize.mini:
        return 6;
    }
  }

  double _getContentVerticalPadding() {
    switch (widget.size) {
      case DropdownSize.large:
        return 8;
      case DropdownSize.medium:
        return 6;
      case DropdownSize.small:
        return 4;
      case DropdownSize.mini:
        return 4;
    }
  }

  TooltipPlacement transPlacement(DropdownPlacement placement) {
    switch (placement) {
      case DropdownPlacement.topLeft:
        return TooltipPlacement.topLeft;
      case DropdownPlacement.topCenter:
        return TooltipPlacement.topCenter;
      case DropdownPlacement.topRight:
        return TooltipPlacement.topRight;

      case DropdownPlacement.rightTop:
        return TooltipPlacement.rightTop;
      case DropdownPlacement.rightCenter:
        return TooltipPlacement.rightCenter;
      case DropdownPlacement.rightBottom:
        return TooltipPlacement.rightBottom;

      case DropdownPlacement.bottomLeft:
        return TooltipPlacement.bottomLeft;
      case DropdownPlacement.bottomCenter:
        return TooltipPlacement.bottomCenter;
      case DropdownPlacement.bottomRight:
        return TooltipPlacement.bottomRight;

      case DropdownPlacement.leftTop:
        return TooltipPlacement.leftTop;
      case DropdownPlacement.leftCenter:
        return TooltipPlacement.leftCenter;
      case DropdownPlacement.leftBottom:
        return TooltipPlacement.leftBottom;
    }
  }

  /// 是否有图标（只提供"图标"或者"文字+图标"模式，所以图标一定会存在）
  bool get hasIcon {
    return true;
  }

  /// 只显示图标
  bool get onlyShowIcon => hasIcon && widget.text.isEmpty;

  /// 按钮内边距
  EdgeInsets get _padding {
    // 根据尺寸获取水平内边距
    double horizontal;

    switch (widget.size) {
      case DropdownSize.large:
        horizontal = 20;
        break;
      case DropdownSize.medium:
        horizontal = 12;
        break;
      case DropdownSize.small:
        horizontal = 8;
        break;
      case DropdownSize.mini:
        horizontal = 4;
        break;
    }

    // 只有图标的情况
    if (onlyShowIcon) return EdgeInsets.all(horizontal / 2);

    // 文字按钮内边距更小
    if (widget.textOnly)
      return EdgeInsets.symmetric(horizontal: horizontal / 2);

    return EdgeInsets.symmetric(horizontal: horizontal);
  }

  /// 选择菜单项
  void _selectItem(DropdownItem item) {
    if (item.disabled) {
      return;
    }

    // 备份选中的item和回调，避免后续操作改变状态
    final selectedItem = item;
    final callback = widget.onItemSelected;

    // 在选择完成后，强制关闭下拉菜单，无论是什么模式
    _forceCloseDropdown();

    // 调用回调
    if (callback != null) {
      callback(selectedItem);
    }
  }

  Widget _buildDropdownItem(
    DropdownItem item,
    bool isSelected,
    AppCustomTooltipController? controller,
  ) {
    // 使用子组件维护hover状态
    return _DropdownItemWidget(
      item: item,
      isSelected: isSelected,
      onSelected: _selectItem,
      itemHeight: _buttonHeight,
      fontSize: _fontSize,
      iconSize: _iconSize,
      borderColor: context.border300,
      contentPadding: _contentPadding,
      controller: controller,
      forceClose: _forceCloseDropdown, // 添加强制关闭回调
    );
  }

  Widget _buildStandardButton(
    Widget constrainedButton,
    Color bgColor,
    BorderRadius borderRadius,
  ) {
    return ClipRRect(
      borderRadius: borderRadius,
      child: Material(
        color: bgColor,
        shape: RoundedRectangleBorder(borderRadius: borderRadius),
        child: InkWell(
          borderRadius: borderRadius,
          onTap: widget.disabled
              ? null
              : () {
                  controller.showTooltip();
                  widget.onPressed?.call();
                  setState(() {
                    _isPressed = false;
                  });
                },
          onTapDown: (_) {
            if (!widget.disabled) {
              setState(() => _isPressed = true);
            }
          },
          onTapUp: (_) {
            setState(() => _isPressed = false);
          },
          onTapCancel: () {
            setState(() => _isPressed = false);
          },
          onHover: (hover) {
            setState(() => _isHovered = hover);
            if (widget.trigger == DropdownTrigger.hover &&
                hover &&
                !controller.isShowTooltip) {
              controller.showTooltip();
              setState(() {
                _isPressed = false;
              });
            } else if (widget.trigger == DropdownTrigger.hover &&
                !hover &&
                controller.isShowTooltip) {
              Future.delayed(Duration(milliseconds: 100), () {
                if (!_isHovered && mounted) {
                  controller.hideTooltip();
                  setState(() {
                    _isPressed = false;
                  });
                }
              });
            }
          },
          mouseCursor: widget.disabled
              ? SystemMouseCursors.forbidden
              : SystemMouseCursors.click,
          child: constrainedButton,
        ),
      ),
    );
  }

  Widget _buildTextButton(Widget constrainedButton, Color textColor) {
    return GestureDetector(
      onTapDown: (_) {
        if (!widget.disabled) {
          setState(() => _isPressed = true);
        }
      },
      onTapUp: (_) {
        setState(() => _isPressed = false);
      },
      onTapCancel: () {
        setState(() => _isPressed = false);
      },
      onTap: widget.disabled
          ? null
          : () {
              controller.showTooltip();
              widget.onPressed?.call();
              setState(() {
                _isPressed = false;
              });
            },
      child: MouseRegion(
        onEnter: (_) {
          setState(() => _isHovered = true);
          if (widget.trigger == DropdownTrigger.hover &&
              !controller.isShowTooltip) {
            controller.showTooltip();
            setState(() {
              _isPressed = false;
            });
          }
        },
        onExit: (_) {
          setState(() => _isHovered = false);
          if (widget.trigger == DropdownTrigger.hover &&
              controller.isShowTooltip) {
            Future.delayed(Duration(milliseconds: 100), () {
              if (!_isHovered && mounted) {
                controller.hideTooltip();
                setState(() {
                  _isPressed = false;
                });
              }
            });
          }
        },
        cursor: widget.disabled
            ? SystemMouseCursors.forbidden
            : SystemMouseCursors.click,
        child: AnimatedOpacity(
          duration: Duration(milliseconds: 150),
          opacity: _isPressed ? 0.7 : 1.0,
          child: constrainedButton,
        ),
      ),
    );
  }
}

/// 下拉项组件，使用StatefulWidget正确处理hover状态
class _DropdownItemWidget extends StatefulWidget {
  final DropdownItem item;
  final bool isSelected;
  final Function(DropdownItem) onSelected;
  final double itemHeight;
  final double fontSize;
  final double iconSize;
  final Color borderColor;
  final double contentPadding;
  final AppCustomTooltipController? controller;
  final VoidCallback forceClose;

  const _DropdownItemWidget({
    Key? key,
    required this.item,
    required this.isSelected,
    required this.onSelected,
    required this.itemHeight,
    required this.fontSize,
    required this.iconSize,
    required this.borderColor,
    required this.contentPadding,
    this.controller,
    required this.forceClose,
  }) : super(key: key);

  @override
  State<_DropdownItemWidget> createState() => _DropdownItemWidgetState();
}

class _DropdownItemWidgetState extends State<_DropdownItemWidget> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // 使用Material组件实现更可靠的hover效果
        Semantics(
          button: true,
          enabled: !widget.item.disabled,
          selected: widget.isSelected,
          label: widget.item.text,
          child: Material(
            color: widget.isSelected
                ? Theme.of(context).colorScheme.surface
                : Colors.transparent,
            child: Stack(
              children: [
                InkWell(
                  onTap: widget.item.disabled
                      ? null
                      : () {
                          // 先调用 onSelected 回调
                          widget.onSelected(widget.item);

                          // 调用强制关闭方法关闭下拉面板，特别是在 hover 模式下
                          widget.forceClose();
                        },
                  onHover: (hover) {
                    setState(() {
                      _isHovered = hover;
                    });
                  },
                  hoverColor: widget.item.disabled
                      ? Colors.transparent
                      : AppColors.primary.withValues(alpha: 0.15),
                  splashColor: widget.item.disabled
                      ? Colors.transparent
                      : AppColors.primary.withValues(alpha: 0.15),
                  highlightColor: widget.item.disabled
                      ? Colors.transparent
                      : AppColors.primary.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(AppRadiusSize.radius2),
                  mouseCursor: widget.item.disabled
                      ? SystemMouseCursors.forbidden
                      : SystemMouseCursors.click,
                  child: Container(
                    height: widget.itemHeight,
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(
                      horizontal: widget.contentPadding,
                    ),
                    alignment: Alignment.centerLeft,
                    child: _buildItemContent(context),
                  ),
                ),

                // 禁用项的半透明覆盖层
                if (widget.item.disabled && _isHovered)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.grey.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(
                          AppRadiusSize.radius2,
                        ),
                      ),
                      // 添加禁止符号图标
                      child: Center(
                        child: Icon(
                          Icons.not_interested,
                          color: Colors.grey.withOpacity(0.5),
                          size: 16,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),

        // 分割线（如果需要）
        if (widget.item.divided)
          Divider(
            height: 1,
            thickness: 1,
            color: context.border300,
            indent: 0,
            endIndent: 0,
          ),
      ],
    );
  }

  Widget _buildItemContent(BuildContext context) {
    // 确定文本颜色 - 无论父组件类型如何，都使用清晰可见的颜色
    Color textColor = widget.item.disabled
        ? AppColors.textDisabled
        : context.textPrimary;

    return Row(
      children: [
        if (widget.item.iconData != null)
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: Icon(
              widget.item.iconData,
              color: textColor,
              size: widget.iconSize,
            ),
          ),
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(bottom: 2),
            child: Text(
              widget.item.text,
              style: TextStyle(
                color: textColor,
                fontSize: widget.fontSize,
                fontWeight: FontWeight.normal,
              ),
              softWrap: false,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ),
      ],
    );
  }
}
