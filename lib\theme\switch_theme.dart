import 'package:flutter/material.dart';
import 'package:jyt_components_package/theme/colors.dart';

/// Switch组件主题配置
class AppSwitchTheme {
  /// 亮色主题配置
  static SwitchThemeData lightSwitchTheme() {
    return SwitchThemeData(
      // 主题色配置
      thumbColor: WidgetStateProperty.resolveWith<Color>((
        Set<WidgetState> states,
      ) {
        if (states.contains(WidgetState.disabled)) {
          return AppColors.white.withValues(alpha: 0.8); // 禁用状态滑块颜色
        }
        if (states.contains(WidgetState.selected)) {
          // 选中状态滑块颜色 - 保持为白色确保对比度
          return AppColors.white;
        }
        return AppColors.white; // 未选中状态滑块颜色
      }),
      // 轨道颜色配置
      trackColor: WidgetStateProperty.resolveWith<Color>((
        Set<WidgetState> states,
      ) {
        if (states.contains(WidgetState.disabled)) {
          return states.contains(WidgetState.selected)
              ? AppColors
                    .primaryDisabled // 禁用且选中状态轨道颜色
              : AppColors.border200; // 禁用且未选中状态轨道颜色
        }
        // 选中+悬停状态使用更深的颜色以增强对比度
        if (states.contains(WidgetState.selected) &&
            states.contains(WidgetState.hovered)) {
          return AppColors.primaryPressed; // 选中且悬停时使用更深的主色调
        }
        return states.contains(WidgetState.selected)
            ? AppColors
                  .primary // 选中状态轨道颜色
            : AppColors.border300; // 未选中状态轨道颜色
      }),
      // 轨道边框配置
      trackOutlineColor: WidgetStateProperty.resolveWith<Color?>((
        Set<WidgetState> states,
      ) {
        // 无边框
        return Colors.transparent;
      }),
      // 轮廓宽度
      trackOutlineWidth: WidgetStateProperty.all(0.0),
      // 滑块图标配置（可选）
      thumbIcon: WidgetStateProperty.all(null),
      // 覆盖颜色
      overlayColor: WidgetStateProperty.resolveWith<Color?>((
        Set<WidgetState> states,
      ) {
        if (states.contains(WidgetState.pressed)) {
          return AppColors.primary.withValues(alpha: 0.1);
        }
        if (states.contains(WidgetState.hovered)) {
          return AppColors.primary.withValues(alpha: 0.05);
        }
        return null;
      }),
    );
  }

  /// 暗色主题配置
  static SwitchThemeData darkSwitchTheme() {
    return SwitchThemeData(
      // 主题色配置
      thumbColor: WidgetStateProperty.resolveWith<Color>((
        Set<WidgetState> states,
      ) {
        if (states.contains(WidgetState.disabled)) {
          return AppColors.backgroundDark300; // 禁用状态滑块颜色
        }
        if (states.contains(WidgetState.selected)) {
          // 选中状态滑块颜色 - 保持为白色确保对比度
          return AppColors.white;
        }
        return AppColors.backgroundDark300; // 未选中状态滑块颜色
      }),
      // 轨道颜色配置
      trackColor: WidgetStateProperty.resolveWith<Color>((
        Set<WidgetState> states,
      ) {
        if (states.contains(WidgetState.disabled)) {
          return states.contains(WidgetState.selected)
              ? AppColors
                    .primaryDisabled // 禁用且选中状态轨道颜色
              : AppColors.borderDark200; // 禁用且未选中状态轨道颜色
        }
        // 选中+悬停状态使用更深的颜色以增强对比度
        if (states.contains(WidgetState.selected) &&
            states.contains(WidgetState.hovered)) {
          return AppColors.primaryPressed; // 选中且悬停时使用更深的主色调
        }
        return states.contains(WidgetState.selected)
            ? AppColors
                  .primary // 选中状态轨道颜色
            : AppColors.borderDark300; // 未选中状态轨道颜色
      }),
      // 轨道边框配置
      trackOutlineColor: WidgetStateProperty.resolveWith<Color?>((
        Set<WidgetState> states,
      ) {
        // 无边框
        return Colors.transparent;
      }),
      // 轮廓宽度
      trackOutlineWidth: WidgetStateProperty.all(0.0),
      // 滑块图标配置（可选）
      thumbIcon: WidgetStateProperty.all(null),
      // 覆盖颜色
      overlayColor: WidgetStateProperty.resolveWith<Color?>((
        Set<WidgetState> states,
      ) {
        if (states.contains(WidgetState.pressed)) {
          return AppColors.primary.withValues(alpha: 0.2);
        }
        if (states.contains(WidgetState.hovered)) {
          return AppColors.primary.withValues(alpha: 0.1);
        }
        return null;
      }),
    );
  }
}
