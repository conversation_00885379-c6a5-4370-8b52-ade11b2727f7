import 'package:flutter/material.dart';
import 'package:jyt_components_package/theme/colors.dart';

class Utils {
  // 计算颜色的亮度
  static double calculateBrightness(Color color) {
    // 获取 RGB 值
    double r = color.r;
    double g = color.g;
    double b = color.b;

    // 使用亮度公式计算
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  // 根据亮度判断字体颜色（浅色或深色）
  static Color getTextColor(Color color) {
    double brightness = calculateBrightness(color);

    // 假设亮度阈值为 128，亮度大于128为浅色，字体设置为黑色，否则为白色
    return brightness > 0.5 ? AppColors.textPrimary : AppColors.textWhite;
  }
}
