import 'package:flutter/material.dart';
import 'package:jyt_components_package/components/others/app_custom_tooltip.dart';
import 'package:jyt_components_package/theme/colors.dart';
import 'package:jyt_components_package/theme/sizes.dart';
import 'package:jyt_components_package/utils/variables.dart';

/// 按钮尺寸枚举，与AppButton保持一致
///
/// 定义了四种按钮尺寸：
/// - large: 大号按钮，高度44px
/// - medium: 中号按钮，默认尺寸
/// - small: 小号按钮，高度24px
/// - mini: 迷你按钮，高度20px
enum ToggleButtonSize {
  large, // 大型按钮
  medium, // 中型按钮
  small, // 小型按钮
  mini, // 迷你按钮
}

/// 自定义切换按钮组件，支持多个图标按钮的单选模式
///
/// 主要用于界面上切换不同视图模式，如看板视图/列表视图等场景
/// 特点：
/// - 支持多个切换选项
/// - 当前选中项高亮显示
/// - 可自定义按钮尺寸
/// - 悬停时显示提示文本
///
/// 使用示例:
/// ```dart
/// AppToggleButton(
///   value: viewModel,
///   btns: [
///     AppToggleButtonItem(value: 1, label: '看板视图', iconData: IconFont.mianxing_shitu_kapian),
///     AppToggleButtonItem(value: 2, label: '列表视图', iconData: IconFont.mianxing_shitu_biaoge),
///   ],
///   size: ToggleButtonSize.medium,
///   onChanged: (value) {
///     setState(() {
///       viewModel = value;
///     });
///   },
/// )
/// ```
class AppToggleButton extends StatelessWidget {
  /// 当前选中按钮对应的值
  final int value;

  /// 点击按钮时的回调
  final ValueChanged<int> onChanged;

  /// 按钮列表
  final List<AppToggleButtonItem> btns;

  /// 按钮尺寸
  final ToggleButtonSize size;

  const AppToggleButton({
    super.key,
    required this.value,
    required this.onChanged,
    required this.btns,
    this.size = ToggleButtonSize.medium, // 默认为中等尺寸
  });

  void _handleTap(int val) {
    if (val != value) {
      onChanged(val);
    }
  }

  /// 获取按钮高度
  double get buttonHeight {
    switch (size) {
      case ToggleButtonSize.large:
        return 44;
      case ToggleButtonSize.medium:
        return Variables.buttonHeight; // 使用现有的中等尺寸高度
      case ToggleButtonSize.small:
        return 24;
      case ToggleButtonSize.mini:
        return 20;
    }
  }

  /// 获取按钮内边距
  double get buttonMargin {
    switch (size) {
      case ToggleButtonSize.large:
        return 4;
      case ToggleButtonSize.medium:
        return AppRadiusSize.radius2; // 使用现有的中等尺寸边距
      case ToggleButtonSize.small:
        return 2;
      case ToggleButtonSize.mini:
        return 1;
    }
  }

  /// 获取图标大小
  double get iconSize {
    switch (size) {
      case ToggleButtonSize.large:
        return AppIconSize.large;
      case ToggleButtonSize.medium:
        return AppIconSize.small; // 保持现有的图标大小
      case ToggleButtonSize.small:
        return AppIconSize.small;
      case ToggleButtonSize.mini:
        return AppIconSize.small * 0.8; // 缩小现有small尺寸作为mini尺寸
    }
  }

  /// 获取按钮圆角
  double get buttonRadius {
    switch (size) {
      case ToggleButtonSize.large:
        return AppRadiusSize.radius4;
      case ToggleButtonSize.medium:
        return AppRadiusSize.radius2; // 保持现有圆角大小
      case ToggleButtonSize.small:
        return AppRadiusSize.radius2;
      case ToggleButtonSize.mini:
        return AppRadiusSize.radius2 * 0.5; // 减半现有radius2作为更小的圆角
    }
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
      child: Container(
        color: context.background300,
        width: buttonHeight * btns.length,
        height: buttonHeight,
        child: Row(
          children: btns.map((item) {
            final bool isSelected = item.value == value;
            Color bgColor = isSelected
                ? context.background100
                : Colors.transparent;
            return Container(
              margin: EdgeInsets.all(buttonMargin),
              width: buttonHeight - (buttonMargin * 2),
              height: buttonHeight - (buttonMargin * 2),
              child: Opacity(
                opacity: item.disabled ? 0.7 : 1,
                child: Material(
                  color: bgColor,
                  borderRadius: BorderRadius.circular(buttonRadius),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(buttonRadius),
                    onTap: () => _handleTap(item.value),
                    mouseCursor: item.disabled
                        ? SystemMouseCursors.forbidden
                        : SystemMouseCursors.click,
                    child: AppCustomTooltip(
                      placement: TooltipPlacement.topCenter,
                      content: item.label,
                      child: Center(
                        child: Icon(
                          item.iconData,
                          size: iconSize,
                          color: isSelected ? context.icon300 : context.icon300,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}

class AppToggleButtonItem {
  final int value;
  final String label;
  final IconData iconData;
  final bool disabled;

  AppToggleButtonItem({
    required this.value,
    required this.label,
    required this.iconData,
    this.disabled = false,
  });
}
