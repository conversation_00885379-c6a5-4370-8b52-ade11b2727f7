import 'package:flutter/material.dart';
import 'package:jyt_components_package/theme/colors.dart';
import 'package:jyt_components_package/theme/sizes.dart';
import 'package:jyt_components_package/utils/image_cache.dart';

/// 支持单个用户头像和多个用户头像的显示
class AppAvatar extends StatelessWidget {
  final List<dynamic> users;
  final double size;
  final double singleAvatarWidth;
  final int maxDisplayCount;

  const AppAvatar({
    super.key,
    required this.users,
    this.size = 24, // 头像大小
    this.singleAvatarWidth = 80, // 只有一项时宽度
    this.maxDisplayCount = 4, // 最多显示的头像数量
  });

  @override
  Widget build(BuildContext context) {
    return users.length <= 1 ? _buildSingleAvatar(context) : _buildMultipleAvatars(context);
  }

  /// 单个头像
  Widget _buildSingleAvatar(BuildContext context) {
    final user = users.isNotEmpty ? users.first : null;
    return Container(
      width: singleAvatarWidth,
      height: size,
      decoration: BoxDecoration(
        color: context.border300,
        borderRadius: BorderRadius.circular(AppRadiusSize.radius12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildAvatarItem(user?.avatar),
          const SizedBox(width: 3),
          Flexible(child: Text(user?.name ?? '', overflow: TextOverflow.ellipsis, maxLines: 1)),
          const SizedBox(width: 10),
        ],
      ),
    );
  }

  /// 多个头像
  Widget _buildMultipleAvatars(BuildContext context) {
    // 偏移量
    final double offsetWidth = size * 0.6;

    // 是否可全部展示
    final bool canShowAll = users.length <= maxDisplayCount;

    // 可显示头像数
    final int displayCount = canShowAll ? users.length : maxDisplayCount;

    // 未展示头像数
    final int hiddenCount = canShowAll ? 0 : users.length - maxDisplayCount;

    return SizedBox(
      width: offsetWidth * displayCount + size, // 包含计数器
      height: size,
      child: Stack(
        children: [
          // 头像列表
          for (int i = 0; i < displayCount; i++)
            Positioned(left: i * offsetWidth, child: _buildAvatarItem(users[i].avatar)),

          // 计数器
          if (!canShowAll)
            Positioned(left: displayCount * offsetWidth, child: _buildCountBadge(hiddenCount)),
        ],
      ),
    );
  }

  /// 构建头像项
  Widget _buildAvatarItem(String? imageUrl) {
    return ImageCacheUtil.cachedAvatarImage(imageUrl: imageUrl, size: size);
  }

  /// 构建计数徽章
  Widget _buildCountBadge(int count) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(size / 2),
      child: Container(
        width: size,
        height: size,
        color: const Color(0xFFCBE9FF),
        child: Center(
          child: Text('+$count', style: TextStyle(color: AppColors.primary, fontSize: 10)),
        ),
      ),
    );
  }
}
