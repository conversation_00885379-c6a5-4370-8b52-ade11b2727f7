import 'package:json_annotation/json_annotation.dart';

part 'user_info.g.dart';

@JsonSerializable()
class UserInfo {
  @Json<PERSON>ey(name: 'Token')
  String? token;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'EmployeeId')
  String? employeeId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'Number')
  String? number;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'Name')
  String? name;
  @<PERSON>son<PERSON>ey(name: 'Avatar')
  String? avatar;
  @Json<PERSON><PERSON>(name: 'Stateenum')
  int? stateenum;

  UserInfo({this.token, this.employeeId, this.number, this.name, this.avatar, this.stateenum});

  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return _$UserInfoFromJson(json);
  }

  Map<String, dynamic> toJson() => _$UserInfoToJson(this);
}
