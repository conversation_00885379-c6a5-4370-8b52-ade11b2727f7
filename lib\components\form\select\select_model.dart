import 'package:flutter/material.dart';

/// 选择器尺寸枚举
enum SelectSize {
  /// 大尺寸
  large,

  /// 中等尺寸（默认）
  medium,

  /// 小尺寸
  small,

  /// 迷你尺寸
  mini,
}

/// 过滤模式枚举
enum SelectFilterMode {
  /// 包含匹配（默认）
  contains,

  /// 开头匹配
  startsWith,

  /// 自定义匹配规则
  custom,

  /// 远程搜索
  remote,
}

/// 选择器选项类
class SelectOption<T> {
  /// 选项值，用于标识选项
  final T value;

  /// 选项文本标签
  final String label;

  /// 图标数据
  final IconData? iconData;

  /// 选项是否禁用
  final bool disabled;

  /// 选项提示信息
  final String? tooltip;

  /// 是否在此选项后添加分隔线
  final bool divided;

  /// 分组名称，用于对选项进行分组
  final String? group;

  /// 自定义数据，可用于存储额外信息
  final Map<String, dynamic>? extra;

  /// 创建选择器选项
  const SelectOption({
    required this.value,
    required this.label,
    this.iconData,
    this.disabled = false,
    this.tooltip,
    this.divided = false,
    this.group,
    this.extra,
  });

  /// 创建分隔线选项
  static SelectOption<T> divider<T>({T? value}) {
    return SelectOption<T>(value: value ?? null as T, label: '', divided: true);
  }

  /// 从选项列表中查找指定值的选项
  static SelectOption<T>? findByValue<T>(List<SelectOption<T>> options, T? value) {
    if (value == null) return null;
    try {
      return options.firstWhere((option) => option.value == value);
    } catch (e) {
      return null;
    }
  }

  /// 从多个选项值构建已选列表
  static List<SelectOption<T>> findMultipleByValues<T>(
    List<SelectOption<T>> options,
    List<T>? values,
  ) {
    if (values == null || values.isEmpty) return [];

    return options.where((option) => values.contains(option.value)).toList();
  }

  @override
  String toString() => 'SelectOption(value: $value, label: $label)';
}
