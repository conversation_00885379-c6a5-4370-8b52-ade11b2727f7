import 'package:flutter/material.dart';
import 'package:jyt_components_package/components/models/tab_item.dart';
import 'package:jyt_components_package/theme/colors.dart';
import 'package:jyt_components_package/theme/sizes.dart';
import 'package:jyt_components_package/utils/icon_font.dart';
import 'package:jyt_components_package/utils/variables.dart';

/// 按钮尺寸枚举
enum TabsSize {
  large, // 大型按钮
  medium, // 中型按钮
}

enum TabsType {
  card, // 显示边框
  text, // 不显示边框
}

class AppTabs extends StatefulWidget {
  /// 组件Items集合
  List<TabItem> items = [];

  /// 当前选中的tab id
  final int selectedId;

  /// 选中tab的回调
  final Function(int) onTabSelected;

  /// 关闭tab的回调
  final Function(int) onTabClosed;

  /// 底边线是否显示
  final bool showBorderLine;

  /// 按钮尺寸
  final TabsSize size;

  /// tabs 风格
  final TabsType type;

  AppTabs({
    super.key,
    this.items = const [],
    required this.selectedId,
    required this.onTabSelected,
    required this.onTabClosed,
    this.showBorderLine = true,
    this.size = TabsSize.medium,
    this.type = TabsType.card,
  });

  @override
  State<AppTabs> createState() => AppTabsState();
}

class AppTabsState extends State<AppTabs> {
  final ScrollController _scrollController = ScrollController();
  bool _showScrollbar = false;

  final double _itemPadding = 8; // 每个item的间距
  final double _itemWidth = 120; // 每个item的宽度

  final _radiusStyle = BorderRadius.circular(AppRadiusSize.radius4);

  // 添加一个 Map 来存储每个 tab 的悬停状态
  final Map<int, bool> _hoverStates = {};

  /// 按钮内边距
  double get _tabsHeight {
    // 根据尺寸获取水平内边距
    double height;

    switch (widget.size) {
      case TabsSize.large:
        height = 44;
        break;
      case TabsSize.medium:
        height = 40;
        break;
    }

    return height;
  }

  @override
  void dispose() {
    _hoverStates.clear(); // 清理悬停状态
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // List<TabItem> tabs = Provider.of<TabsProvider>(context, listen: false).items;

    return RawScrollbar(
      controller: _scrollController,
      thumbVisibility: _showScrollbar,
      thumbColor: Colors.grey[400]!,
      radius: Radius.circular(20),
      thickness: 8,
      minThumbLength: 50,
      fadeDuration: Duration(milliseconds: 300),
      timeToFade: Duration(milliseconds: 1000),
      child: SingleChildScrollView(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        physics: AlwaysScrollableScrollPhysics(), // 允许直接滚动
        child: MouseRegion(
          onEnter: (_) => setState(() => _showScrollbar = true),
          onExit: (_) => setState(() => _showScrollbar = false),

          // child: Selector<TabsProvider, List<TabItem>>(
          //   shouldRebuild: (ore, next) => true,
          //   selector: (context, provider) => provider.items,
          //   builder: (context, tabList, child) {
          //     return Row(children: _buildTabItemList(context, tabList));
          //   },
          // ),
          child: Container(
            decoration: widget.showBorderLine
                ? BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: context.border300),
                    ),
                  )
                : null,
            child: Row(
              children: [
                Wrap(
                  spacing: _itemPadding,
                  children: [
                    Padding(padding: EdgeInsets.only(left: _itemPadding / 2)),
                    ..._buildTabItemList(context, widget.items),
                    Padding(padding: EdgeInsets.only(left: _itemPadding / 2)),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  _buildTabItemList(BuildContext context, List<TabItem> items) {
    // final tabsProvider = Provider.of<TabsProvider>(context, listen: false);
    // List<Widget> widgetList = []; // 用于存储生成的widget
    // for (var i = 0; i < items.length; i++) {
    //   widgetList.add(
    //     Selector<TabsProvider, TabItem>(
    //       selector: (context, provider) => provider.items[i],
    //       builder: (context, tab, child) {
    //         return _buildTabItem(
    //           context: context, // 传递 context
    //           tab: items[i],
    //           isChecked: items[i].id == tabsProvider.currentCheckedId, // 直接比较
    //           onTap: () => tabsProvider.changeCheckedId(items[i].id), // 调用 Provider 方法
    //           isFirst: i == 0,
    //         );
    //       },
    //     ),
    //   );
    // }
    // return widgetList;

    // 直接使用 map 构建列表，移除内部 Selector
    return List.generate(items.length, (index) {
      // 获取 Provider 实例，但仅用于 onTap 回调，所以 listen: false

      // final tabsProvider = Provider.of<TabsProvider>(context, listen: false);

      // items[index].id == tabsProvider.currentCheckedId
      // tabsProvider.changeCheckedId(items[index].id)

      return _buildTabItem(
        context: context, // 传递 context
        tab: items[index],
        isChecked: items[index].id == widget.selectedId, // 直接比较
        onTap: () => widget.onTabSelected(items[index].id), // 调用 Provider 方法
        isFirst: index == 0,
      );
    });
  }

  Widget _buildTabItem({
    required BuildContext context,
    required TabItem tab,
    required bool isChecked,
    required VoidCallback onTap,
    bool isFirst = false,
  }) {
    return StatefulBuilder(
      builder: (context, setState) {
        /// tab项边框样式
        final BorderSide borderStyle = BorderSide(
          color: widget.type == TabsType.card
              ? context.border300
              : Colors.transparent,
          width: 1,
        );

        // 使用 tab.id 作为 key 来获取悬停状态
        bool isHovered = _hoverStates[tab.id] ?? false;

        return Container(
          // margin: EdgeInsets.only(top: 5),
          child: Material(
            color: widget.type == TabsType.card
                ? (isChecked ? Colors.transparent : context.background200)
                : null,
            borderRadius: _radiusStyle,
            child: InkWell(
              borderRadius: _radiusStyle,
              hoverColor: widget.type == TabsType.card
                  ? context.background200
                  : Colors.transparent,
              onTap: onTap,
              child: MouseRegion(
                cursor: SystemMouseCursors.click,
                // 更新状态使用 setState
                onEnter: (_) {
                  setState(() {
                    _hoverStates[tab.id] = true;
                  });
                },
                onExit: (_) {
                  setState(() {
                    _hoverStates[tab.id] = false;
                  });
                },
                child: Container(
                  decoration: BoxDecoration(
                    // color: Colors.blue, // 设置背景色
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(AppRadiusSize.radius4),
                      topRight: Radius.circular(AppRadiusSize.radius4),
                    ), // 圆角
                    border: Border(
                      left: borderStyle,
                      top: borderStyle,
                      right: borderStyle,
                    ),
                  ),
                  width: _itemWidth,
                  height: _tabsHeight,
                  alignment: Alignment.center,
                  child: Padding(
                    padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(top: 2),
                          child: Icon(
                            IconFont.xianxing_tixing,
                            size: AppIconSize.small,
                            color: widget.type == TabsType.text && isChecked
                                ? AppColors.primary
                                : isHovered
                                ? AppColors.primary.withValues(alpha: 0.85)
                                : context.icon300,
                          ),
                        ),
                        SizedBox(width: Variables.iconTextSpace),
                        Flexible(
                          child: Text(
                            tab.title,
                            style: TextStyle(
                              overflow: TextOverflow.ellipsis,
                              color: widget.type == TabsType.text
                                  ? (isChecked
                                        ? AppColors.primary
                                        : isHovered
                                        ? AppColors.primary.withValues(
                                            alpha: 0.85,
                                          )
                                        : context.textPrimary)
                                  : null,
                            ),
                          ),
                        ),

                        Visibility(
                          visible: isHovered,
                          maintainSize: false, // 不保持空间
                          maintainAnimation: false,
                          maintainState: false,
                          child: InkWell(
                            onTap: () {
                              // 处理关闭按钮点击事件
                              // final provider = context.read<TabsProvider>();
                              //如果删除的是当前选中的tab，那么把当前选中的切换到第一个tab

                              // provider.currentCheckedId == tab.id
                              if (widget.selectedId == tab.id) {
                                final otherTabs = widget.items.where(
                                  (element) => element.id != tab.id,
                                );

                                if (otherTabs.isNotEmpty) {
                                  var firstTabId = otherTabs.first.id;
                                  // provider.changeCheckedId(firstTabId);
                                  widget.onTabSelected(firstTabId);
                                  scrollToTab(firstTabId);
                                } else {
                                  // provider.changeCheckedId(-1);
                                  widget.onTabSelected(-1);
                                }
                              }

                              /// 当鼠标点击tab上的关闭按钮时，删除tab，后面的tab会顶上来，此时需要检查鼠标是否在顶上来的tab上，不然顶上来的tab，虽然鼠标在该tab上，但是不会显示删除按钮，不方便连续删除操作
                              // 获取当前鼠标位置
                              final RenderBox? renderBox =
                                  context.findRenderObject() as RenderBox?;
                              if (renderBox != null) {
                                final position = renderBox.localToGlobal(
                                  Offset.zero,
                                );

                                // 删除 tab
                                // final provider = context.read<TabsProvider>();
                                // provider.removeItem(tab.id);
                                widget.onTabClosed(tab.id);

                                // 在下一帧检查鼠标位置
                                WidgetsBinding.instance.addPostFrameCallback((
                                  _,
                                ) {
                                  _checkMousePosition(position);
                                });
                              }
                            },
                            child: Padding(
                              padding: EdgeInsets.only(top: 2),
                              child: Icon(
                                IconFont.xianxing_guanbi,
                                size: AppIconSize.small,
                                color: context.icon300,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // 添加方法检查鼠标是否在某个 tab 上
  void _checkMousePosition(Offset globalPosition) {
    if (!mounted) return;

    setState(() {
      // 清除所有悬停状态
      _hoverStates.clear();

      // 获取所有 tab 的位置并检查鼠标是否在其上
      final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
      if (renderBox == null) return;

      final localPosition = renderBox.globalToLocal(globalPosition);

      // 获取当前所有的 tabs
      // final tabsProvider = context.read<TabsProvider>();
      // final items = tabsProvider.items;
      final items = widget.items;

      // 计算每个 tab 的位置并检查鼠标是否在其上
      double currentX = _itemPadding;
      for (var item in items) {
        final tabWidth = _itemWidth + _itemPadding;
        if (localPosition.dx >= currentX &&
            localPosition.dx < currentX + tabWidth &&
            localPosition.dy >= 0 &&
            localPosition.dy <= Variables.titleHeight) {
          _hoverStates[item.id] = true;
          break;
        }
        currentX += tabWidth;
      }
    });
  }

  // 如果需要滚动到特定tab的方法
  void scrollToTab(int tabId) {
    if (!mounted) return;

    // var provider = context.read<TabsProvider>();
    var index = widget.items.indexWhere((element) => element.id == tabId);
    if (index == -1) return; // 如果找不到索引，则不滚动

    final double itemWidth = _itemPadding + _itemWidth; // 120 宽度 + 5 边距
    _scrollController.animateTo(
      index * itemWidth,
      duration: Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }
}
