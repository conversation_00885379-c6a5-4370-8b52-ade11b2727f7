import 'package:flutter/material.dart';
import 'package:jyt_components_package/theme/colors.dart';
import 'package:jyt_components_package/theme/input_theme.dart';
import 'select_model.dart';

/// 选择器样式助手类
class SelectStyle {
  /// 获取选择器高度
  static double getSelectorHeight(SelectSize size) {
    switch (size) {
      case SelectSize.large:
        return 44.0;
      case SelectSize.medium:
        return 36.0;
      case SelectSize.small:
        return 28.0;
      case SelectSize.mini:
        return 24.0;
    }
  }

  /// 获取字体大小
  static double getFontSize(SelectSize size) {
    switch (size) {
      case SelectSize.large:
        return 16;
      case SelectSize.medium:
        return 14;
      case SelectSize.small:
        return 12;
      case SelectSize.mini:
        return 12;
    }
  }

  /// 获取边框颜色
  static Color getBorderColor(
    BuildContext context, {
    required bool isFocused,
    bool disabled = false,
  }) {
    if (disabled) {
      return context.border300;
    }
    if (isFocused) {
      return AppColors.primary;
    }
    return context.border300;
  }

  /// 获取背景色
  static Color getBackgroundColor(BuildContext context, {bool disabled = false}) {
    if (disabled) {
      return Theme.of(context).brightness == Brightness.light
          ? AppInputTheme.disabledFillColorLight
          : AppInputTheme.disabledFillColorDark;
    }
    return Theme.of(context).inputDecorationTheme.fillColor ?? Colors.transparent;
  }

  /// 获取占位符文本样式
  static TextStyle getPlaceholderStyle(
    BuildContext context, {
    required double fontSize,
    bool disabled = false,
  }) {
    return TextStyle(
      fontSize: fontSize,
      color: disabled ? Theme.of(context).disabledColor : AppColors.textHint,
    );
  }

  /// 获取标签文本样式
  static TextStyle getLabelStyle(BuildContext context, {required double fontSize}) {
    return TextStyle(fontSize: fontSize, color: AppColors.textSecondary);
  }

  /// 获取值文本样式
  static TextStyle getValueStyle(
    BuildContext context, {
    required double fontSize,
    bool disabled = false,
  }) {
    return TextStyle(
      fontSize: fontSize,
      color: disabled ? Theme.of(context).disabledColor : context.textPrimary,
    );
  }
}
