import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'app_input_number_style.dart';

/// 数字输入计数器组件
///
/// 功能特性：
/// - 支持按钮点击和手动输入两种方式
/// - 可配置最小值、最大值和步长
/// - 自动禁用超出范围的按钮
/// - 支持禁用状态和自定义样式
/// - 包含输入验证和格式化
class AppInputNumber extends StatefulWidget {
  /// 当前数值
  final double value;

  /// 数值变化回调
  final ValueChanged<double>? onChanged;

  /// 是否禁用组件
  final bool disabled;

  /// 最小值限制
  final double? min;

  /// 最大值限制
  final double? max;

  /// 每次增减的步长
  final double step;

  /// 小数位数精度
  final int precision;

  /// 输入框宽度
  final double? width;

  /// 输入框高度
  final double? height;

  /// 占位符文本
  final String? placeholder;

  /// 是否显示控制按钮
  final bool showControls;

  /// 按钮位置：'right' 右侧垂直排列, 'both' 两侧水平排列
  final String controlsPosition;

  /// 输入框样式
  final InputDecoration? decoration;

  /// 文本样式
  final TextStyle? textStyle;

  /// 组件尺寸
  final InputNumberSize size;

  const AppInputNumber({
    super.key,
    this.value = 0,
    this.onChanged,
    this.disabled = false,
    this.min,
    this.max,
    this.step = 1,
    this.precision = 0,
    this.width,
    this.height,
    this.placeholder,
    this.showControls = true,
    this.controlsPosition = 'both', // 'right' | 'both'
    this.decoration,
    this.textStyle,
    this.size = InputNumberSize.small,
  });

  @override
  State<AppInputNumber> createState() => _AppInputNumberState();
}

class _AppInputNumberState extends State<AppInputNumber> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  double _currentValue = 0;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.value;
    _controller = TextEditingController(text: _formatValue(_currentValue));
    _focusNode = FocusNode();

    // 监听焦点变化，失去焦点时验证输入
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void didUpdateWidget(AppInputNumber oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _currentValue = widget.value;
      _controller.text = _formatValue(_currentValue);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// 格式化数值显示
  String _formatValue(double value) {
    if (widget.precision == 0) {
      return value.toInt().toString();
    }
    return value.toStringAsFixed(widget.precision);
  }

  /// 修正浮点数精度问题
  /// 根据组件的precision参数对数值进行精度修正
  double _fixPrecision(double value) {
    if (widget.precision == 0) {
      return value.roundToDouble();
    }

    // 使用字符串转换的方式来避免浮点数精度问题
    // 先格式化为指定精度的字符串，再转换回double
    final formatted = value.toStringAsFixed(widget.precision);
    return double.parse(formatted);
  }

  /// 验证数值是否在有效范围内
  double _validateValue(double value) {
    if (widget.min != null && value < widget.min!) {
      return widget.min!;
    }
    if (widget.max != null && value > widget.max!) {
      return widget.max!;
    }
    return value;
  }

  /// 焦点变化处理
  void _onFocusChanged() {
    if (!_focusNode.hasFocus) {
      _validateAndUpdateFromInput();
    }
    // 触发重绘以更新边框颜色
    setState(() {});
  }

  /// 从输入框验证并更新数值
  void _validateAndUpdateFromInput() {
    final text = _controller.text.trim();
    if (text.isEmpty) {
      // 空输入时使用最小值或0
      final defaultValue = widget.min ?? 0;
      _updateValue(defaultValue);
      return;
    }

    final parsedValue = double.tryParse(text);
    if (parsedValue != null) {
      _updateValue(parsedValue);
    } else {
      // 无效输入时恢复原值
      _controller.text = _formatValue(_currentValue);
    }
  }

  /// 更新数值
  void _updateValue(double newValue) {
    // 先修正精度，再进行验证
    final precisionFixedValue = _fixPrecision(newValue);
    final validatedValue = _validateValue(precisionFixedValue);
    if (validatedValue != _currentValue) {
      setState(() {
        _currentValue = validatedValue;
        _controller.text = _formatValue(_currentValue);
      });
      widget.onChanged?.call(_currentValue);
    } else if (_controller.text != _formatValue(_currentValue)) {
      // 确保显示格式正确
      _controller.text = _formatValue(_currentValue);
    }
  }

  /// 增加数值
  void _increment() {
    if (widget.disabled) return;
    final rawValue = _currentValue + widget.step;
    final newValue = _fixPrecision(rawValue); // 修正浮点数精度
    if (widget.max == null || newValue <= widget.max!) {
      _updateValue(newValue);
    }
  }

  /// 减少数值
  void _decrement() {
    if (widget.disabled) return;
    final rawValue = _currentValue - widget.step;
    final newValue = _fixPrecision(rawValue); // 修正浮点数精度
    if (widget.min == null || newValue >= widget.min!) {
      _updateValue(newValue);
    }
  }

  /// 检查是否可以增加
  bool get _canIncrement {
    if (widget.disabled) return false;
    if (widget.max == null) return true;
    final nextValue = _fixPrecision(_currentValue + widget.step);
    return nextValue <= widget.max!;
  }

  /// 检查是否可以减少
  bool get _canDecrement {
    if (widget.disabled) return false;
    if (widget.min == null) return true;
    final nextValue = _fixPrecision(_currentValue - widget.step);
    return nextValue >= widget.min!;
  }

  /// 获取边框颜色
  Color _getBorderColor() {
    return AppInputNumberStyle.getBorderColor(
      disabled: widget.disabled,
      focused: _focusNode.hasFocus,
    );
  }

  /// 获取当前组件的实际高度
  double get _actualHeight =>
      widget.height ?? AppInputNumberStyle.getHeightBySize(widget.size);

  /// 获取当前组件的实际字体大小
  double get _actualFontSize =>
      widget.textStyle?.fontSize ??
      AppInputNumberStyle.getFontSizeBySize(widget.size);

  /// 获取当前组件的实际按钮宽度
  double get _actualButtonWidth =>
      AppInputNumberStyle.getButtonWidthBySize(widget.size);

  /// 获取当前组件的实际图标大小
  double get _actualIconSize =>
      AppInputNumberStyle.getIconSizeBySize(widget.size);

  /// 获取当前组件的实际垂直按钮宽度
  double get _actualVerticalButtonWidth =>
      AppInputNumberStyle.getVerticalButtonWidthBySize(widget.size);

  /// 获取当前组件的实际水平内边距
  double get _actualHorizontalPadding =>
      AppInputNumberStyle.getHorizontalPaddingBySize(widget.size);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      height: _actualHeight,
      child: widget.controlsPosition == 'both'
          ? _buildHorizontalLayout()
          : _buildVerticalLayout(),
    );
  }

  /// 构建水平布局（两侧按钮）
  Widget _buildHorizontalLayout() {
    final isFocused = _focusNode.hasFocus;

    return Container(
      // 使用固定的外边距来补偿边框宽度变化，避免抖动
      margin: EdgeInsets.all(AppInputNumberStyle.getMargin(focused: isFocused)),
      decoration: BoxDecoration(
        border: Border.all(
          color: _getBorderColor(),
          width: AppInputNumberStyle.getBorderWidth(focused: isFocused),
        ),
        borderRadius: AppInputNumberStyle.getContainerBorderRadius(),
        color: AppInputNumberStyle.getBackgroundColor(
          disabled: widget.disabled,
        ),
      ),
      child: Row(
        children: [
          // 左侧减号按钮
          if (widget.showControls)
            _buildControlButton(
              icon: Icons.remove,
              onPressed: _canDecrement ? _decrement : null,
              isLeft: true,
            ),

          // 中间输入框
          Expanded(child: _buildInputField()),

          // 右侧加号按钮
          if (widget.showControls)
            _buildControlButton(
              icon: Icons.add,
              onPressed: _canIncrement ? _increment : null,
              isRight: true,
            ),
        ],
      ),
    );
  }

  /// 构建垂直布局（右侧垂直按钮）
  Widget _buildVerticalLayout() {
    final isFocused = _focusNode.hasFocus;

    return Container(
      // 使用固定的外边距来补偿边框宽度变化，避免抖动
      margin: EdgeInsets.all(AppInputNumberStyle.getMargin(focused: isFocused)),
      decoration: BoxDecoration(
        border: Border.all(
          color: _getBorderColor(),
          width: AppInputNumberStyle.getBorderWidth(focused: isFocused),
        ),
        borderRadius: AppInputNumberStyle.getContainerBorderRadius(),
        color: AppInputNumberStyle.getBackgroundColor(
          disabled: widget.disabled,
        ),
      ),
      child: Row(
        children: [
          // 输入框
          Expanded(child: _buildInputFieldForVertical()),

          // 右侧垂直按钮组
          if (widget.showControls) _buildVerticalControls(),
        ],
      ),
    );
  }

  /// 构建垂直控制按钮组
  Widget _buildVerticalControls() {
    // 内部分隔线始终使用固定的灰色，不受焦点状态影响
    final separatorColor = AppInputNumberStyle.getSeparatorColor(
      disabled: widget.disabled,
    );

    return Container(
      width: _actualVerticalButtonWidth,
      height: _actualHeight,
      decoration: BoxDecoration(
        // 添加左侧分隔线，与输入框分开
        border: Border(left: BorderSide(color: separatorColor, width: 1)),
        borderRadius: AppInputNumberStyle.getVerticalControlsBorderRadius(),
      ),
      child: Column(
        children: [
          // 上方加号按钮
          _buildVerticalControlButton(
            icon: Icons.keyboard_arrow_up,
            onPressed: _canIncrement ? _increment : null,
            isTop: true,
          ),

          // 分隔线
          Container(height: 1, color: separatorColor),

          // 下方减号按钮
          _buildVerticalControlButton(
            icon: Icons.keyboard_arrow_down,
            onPressed: _canDecrement ? _decrement : null,
            isBottom: true,
          ),
        ],
      ),
    );
  }

  /// 构建输入框（用于水平布局，无边框）
  Widget _buildInputField() {
    // 计算合适的垂直内边距以确保文字垂直居中
    final fontSize = _actualFontSize;
    final inputHeight = _actualHeight;
    final verticalPadding = AppInputNumberStyle.calculateVerticalPadding(
      height: inputHeight,
      fontSize: fontSize,
    );

    return Container(
      height: inputHeight,
      alignment: Alignment.center,
      child: TextFormField(
        controller: _controller,
        focusNode: _focusNode,
        enabled: !widget.disabled,
        textAlign: TextAlign.center,
        textAlignVertical: TextAlignVertical.center,
        style: AppInputNumberStyle.getTextStyle(customStyle: widget.textStyle),
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*')),
        ],
        decoration:
            widget.decoration ??
            InputDecoration(
              hintText: widget.placeholder,
              hintStyle: AppInputNumberStyle.getHintStyle(fontSize: fontSize),
              contentPadding: EdgeInsets.symmetric(
                horizontal: _actualHorizontalPadding,
                vertical: verticalPadding,
              ),
              border: InputBorder.none,
              enabledBorder: InputBorder.none,
              focusedBorder: InputBorder.none,
              disabledBorder: InputBorder.none,
              errorBorder: InputBorder.none,
              focusedErrorBorder: InputBorder.none,
              filled: false,
              isDense: true,
            ),
        onChanged: (value) {
          // 实时验证输入（可选）
          setState(() {}); // 触发重绘以更新边框颜色
        },
        onFieldSubmitted: (value) {
          _validateAndUpdateFromInput();
        },
      ),
    );
  }

  /// 构建输入框（用于垂直布局，无边框）
  Widget _buildInputFieldForVertical() {
    // 计算合适的垂直内边距以确保文字垂直居中
    final fontSize = _actualFontSize;
    final inputHeight = _actualHeight;
    final verticalPadding = AppInputNumberStyle.calculateVerticalPadding(
      height: inputHeight,
      fontSize: fontSize,
    );

    return Container(
      height: inputHeight,
      alignment: Alignment.center,
      child: TextFormField(
        controller: _controller,
        focusNode: _focusNode,
        enabled: !widget.disabled,
        textAlign: TextAlign.center,
        textAlignVertical: TextAlignVertical.center,
        style: AppInputNumberStyle.getTextStyle(customStyle: widget.textStyle),
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*')),
        ],
        decoration:
            widget.decoration ??
            InputDecoration(
              hintText: widget.placeholder,
              hintStyle: AppInputNumberStyle.getHintStyle(fontSize: fontSize),
              contentPadding: EdgeInsets.symmetric(
                horizontal: _actualHorizontalPadding,
                vertical: verticalPadding,
              ),
              border: InputBorder.none,
              enabledBorder: InputBorder.none,
              focusedBorder: InputBorder.none,
              disabledBorder: InputBorder.none,
              errorBorder: InputBorder.none,
              focusedErrorBorder: InputBorder.none,
              filled: false,
              isDense: true,
            ),
        onChanged: (value) {
          // 实时验证输入（可选）
          setState(() {}); // 触发重绘以更新边框颜色
        },
        onFieldSubmitted: (value) {
          _validateAndUpdateFromInput();
        },
      ),
    );
  }

  /// 构建水平控制按钮
  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback? onPressed,
    bool isLeft = false,
    bool isRight = false,
  }) {
    // 内部分隔线始终使用固定的灰色，不受焦点状态影响
    final separatorColor = AppInputNumberStyle.getSeparatorColor(
      disabled: widget.disabled,
    );
    final borderRadius = isLeft
        ? AppInputNumberStyle.getLeftButtonBorderRadius()
        : isRight
        ? AppInputNumberStyle.getRightButtonBorderRadius()
        : BorderRadius.zero;

    return Container(
      width: _actualButtonWidth,
      height: _actualHeight,
      decoration: BoxDecoration(
        // 添加内部分隔线，使用固定颜色
        border: Border(
          right: isLeft && !isRight
              ? BorderSide(color: separatorColor, width: 1)
              : BorderSide.none,
          left: isRight && !isLeft
              ? BorderSide(color: separatorColor, width: 1)
              : BorderSide.none,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: borderRadius,
        child: InkWell(
          onTap: onPressed,
          borderRadius: borderRadius,
          child: Container(
            width: _actualButtonWidth,
            height: _actualHeight,
            alignment: Alignment.center,
            child: Icon(
              icon,
              size: _actualIconSize,
              color: AppInputNumberStyle.getIconColor(
                enabled: onPressed != null,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建垂直控制按钮
  Widget _buildVerticalControlButton({
    required IconData icon,
    required VoidCallback? onPressed,
    bool isTop = false,
    bool isBottom = false,
  }) {
    final borderRadius = isTop
        ? AppInputNumberStyle.getVerticalTopButtonBorderRadius()
        : isBottom
        ? AppInputNumberStyle.getVerticalBottomButtonBorderRadius()
        : BorderRadius.zero;

    return Expanded(
      child: Material(
        color: AppInputNumberStyle.getBackgroundColor(
          disabled: onPressed == null,
        ),
        borderRadius: borderRadius,
        child: InkWell(
          onTap: onPressed,
          borderRadius: borderRadius,
          child: Container(
            width: _actualVerticalButtonWidth,
            alignment: Alignment.center,
            child: Icon(
              icon,
              size: _actualIconSize * 0.75, // 垂直按钮图标稍小一些
              color: AppInputNumberStyle.getIconColor(
                enabled: onPressed != null,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
