import 'package:flutter/material.dart';
import 'package:jyt_components_package/theme/colors.dart';

/// 徽章组件，用于显示数字或文本
class AppBadge extends StatelessWidget {
  /// 构造函数
  const AppBadge({
    Key? key,
    this.value,
    this.color,
    this.height = 20.0,
    this.textStyle,
  }) : super(key: key);

  /// 徽章显示的值，支持多种类型:
  /// - int: 显示数字
  /// - String: 显示文本
  /// - bool: true显示"✓"，false显示"✗"
  /// - null: 不显示内容，只显示徽章
  final dynamic value;

  /// 徽章背景颜色
  final Color? color;

  /// 徽章大小
  final double height;

  /// 文字样式
  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context) {
    // 处理不同类型的value
    String displayText = '';

    if (value == null) {
      displayText = '';
    } else if (value is int) {
      // 数字处理，超过99显示99+
      final intValue = value as int;
      displayText = intValue > 99 ? '99+' : intValue.toString();
    } else if (value is String) {
      // 字符串直接显示
      displayText = value as String;
    } else if (value is bool) {
      // 布尔值显示为对号或叉号
      displayText = (value as bool) ? '✓' : '✗';
    } else {
      // 其他类型转为字符串
      displayText = value.toString();
    }

    // 根据是否有内容决定徽章大小
    final bool hasContent = displayText.isNotEmpty;

    final defaultColor = AppColors.accent;
    final badgeColor = color ?? defaultColor;

    final defaultTextStyle = TextStyle(
      color: Colors.white,
      fontSize: 12,
      fontWeight: FontWeight.bold,
    );
    final finalTextStyle = textStyle ?? defaultTextStyle;

    return Container(
      width: hasContent ? null : 8,
      height: height,
      constraints: hasContent
          ? BoxConstraints(minWidth: 8, minHeight: 8)
          : null,
      padding: hasContent
          ? EdgeInsets.only(left: 8, right: 8, bottom: 3)
          : null,
      decoration: BoxDecoration(
        color: badgeColor,
        shape: hasContent ? BoxShape.rectangle : BoxShape.circle,
        borderRadius: hasContent ? BorderRadius.circular(10) : null,
      ),
      alignment: Alignment.center,
      child: hasContent ? Text(displayText, style: finalTextStyle) : null,
    );
  }
}

/// 使用示例:
///
/// // 数字徽章
/// AppBadge(value: 5)
///
/// // 文本徽章
/// AppBadge(value: "New")
///
/// // 布尔徽章
/// AppBadge(value: true)
///
/// // 空徽章（只显示一个圆点）
/// AppBadge()
