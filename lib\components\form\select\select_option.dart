import 'package:flutter/material.dart';
import 'package:jyt_components_package/components/form/select/select_model.dart';
import 'package:jyt_components_package/theme/colors.dart';
import 'package:jyt_components_package/theme/sizes.dart';

/// 选项样式扩展，用于处理样式相关的计算
extension SelectOptionStyleExtension on SelectOption {
  /// 获取图标颜色
  Color getIconColor(BuildContext context, {required bool selected}) {
    if (disabled) {
      return AppColors.textDisabled;
    }
    return selected ? AppColors.primary : AppColors.icon300;
  }

  /// 获取文本样式
  TextStyle getTextStyle(BuildContext context, {required bool selected}) {
    TextStyle style = TextStyle(
      fontSize: 14,
      color: disabled ? Theme.of(context).disabledColor : context.textPrimary,
    );

    if (selected) {
      style = style.copyWith(
        fontWeight: FontWeight.w500,
        color: disabled ? AppColors.textDisabled : AppColors.primary,
      );
    }

    return style;
  }
}

/// 选项项组件
///
/// 渲染下拉选择器中的单个选项，处理选中状态、禁用状态等样式。
class SelectOptionItem<T> extends StatelessWidget {
  /// 选项数据
  final SelectOption<T> option;

  /// 是否选中
  final bool selected;

  /// 选项点击回调
  final VoidCallback? onTap;

  /// 选项多选状态
  final bool multiple;

  /// 是否显示选中图标
  final bool showSelectedIcon;

  /// 选项模板构建器
  final Widget Function(
    BuildContext context,
    SelectOption<T> option,
    bool selected,
  )?
  itemBuilder;

  /// 构造函数
  const SelectOptionItem({
    super.key,
    required this.option,
    this.selected = false,
    this.onTap,
    this.multiple = false,
    this.showSelectedIcon = true,
    this.itemBuilder,
  });

  @override
  Widget build(BuildContext context) {
    // 如果是分隔线选项，则渲染一个分隔线
    if (option.divided) {
      return const Divider(height: 1);
    }

    // 使用自定义构建器，或默认渲染
    final Widget content = itemBuilder != null
        ? itemBuilder!(context, option, selected)
        : _buildDefaultOption(context);

    // 如果禁用则应用禁用样式，否则应用点击交互
    return option.disabled
        ? _wrapWithDisabled(context, content)
        : InkWell(
            onTap: onTap,
            hoverColor: Colors.blue.withOpacity(0.1),
            child: content,
          );
  }

  /// 构建默认选项渲染
  Widget _buildDefaultOption(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          // 1. 显示图标
          if (option.iconData != null) ...[
            Icon(
              option.iconData,
              size: AppIconSize.small,
              color: option.getIconColor(context, selected: selected),
            ),
            const SizedBox(width: 8),
          ],

          // 2. 显示选项标签
          Expanded(
            child: Text(
              option.label,
              style: option.getTextStyle(context, selected: selected),
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // 3. 选中状态下显示勾选图标（无论单选还是多选）
          if (selected && showSelectedIcon) ...[
            const SizedBox(width: 8),
            Icon(
              Icons.check,
              size: 16,
              color: option.getIconColor(context, selected: selected),
            ),
          ],

          // 4. 显示tooltip
          if (option.tooltip != null) ...[
            const SizedBox(width: 8),
            Tooltip(
              message: option.tooltip!,
              child: Icon(Icons.info_outline, size: 16, color: AppColors.info),
            ),
          ],
        ],
      ),
    );
  }

  /// 禁用状态的包装器
  Widget _wrapWithDisabled(BuildContext context, Widget child) {
    return Container(
      color: Colors.grey.withOpacity(0.05),
      child: Opacity(opacity: 0.5, child: child),
    );
  }
}
