import 'package:flutter/material.dart';
import 'package:jyt_components_package/theme/colors.dart';

/// 输入框主题
class AppInputTheme {
  /// 禁用状态填充色 - 亮色主题
  static final Color disabledFillColorLight = AppColors.background300;

  /// 禁用状态填充色 - 暗色主题
  static final Color disabledFillColorDark = AppColors.backgroundDark300;

  /// 亮色输入框主题
  static InputDecorationTheme lightInputTheme() {
    return InputDecorationTheme(
      filled: true,
      fillColor: AppColors.white,
      border: OutlineInputBorder(
        borderSide: BorderSide(color: AppColors.border200),
        borderRadius: BorderRadius.circular(4),
      ),
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppColors.border200),
        borderRadius: BorderRadius.circular(4),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppColors.primary),
        borderRadius: BorderRadius.circular(4),
      ),
      errorBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppColors.error),
        borderRadius: BorderRadius.circular(4),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppColors.error),
        borderRadius: BorderRadius.circular(4),
      ),
      disabledBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppColors.border300.withValues(alpha: .5)),
        borderRadius: BorderRadius.circular(4),
      ),
      hintStyle: TextStyle(color: AppColors.textHint),
      labelStyle: TextStyle(color: AppColors.textSecondary),
      errorStyle: TextStyle(color: AppColors.error, fontSize: 12),
    );
  }

  /// 暗色输入框主题
  static InputDecorationTheme darkInputTheme() {
    return InputDecorationTheme(
      filled: true,
      fillColor: AppColors.backgroundDark300,
      border: OutlineInputBorder(
        borderSide: BorderSide(color: AppColors.borderDark300),
        borderRadius: BorderRadius.circular(4),
      ),
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppColors.borderDark300),
        borderRadius: BorderRadius.circular(4),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppColors.primary),
        borderRadius: BorderRadius.circular(4),
      ),
      errorBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppColors.error),
        borderRadius: BorderRadius.circular(4),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppColors.error),
        borderRadius: BorderRadius.circular(4),
      ),
      disabledBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppColors.borderDark300.withValues(alpha: .5)),
        borderRadius: BorderRadius.circular(4),
      ),
      hintStyle: TextStyle(color: AppColors.textHint),
      labelStyle: TextStyle(color: AppColors.textSecondary),
      errorStyle: TextStyle(color: AppColors.error, fontSize: 12),
    );
  }
}
