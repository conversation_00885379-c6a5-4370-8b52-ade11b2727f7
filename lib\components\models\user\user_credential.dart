import 'package:json_annotation/json_annotation.dart';

part 'user_credential.g.dart';

@JsonSerializable()
/// 用户登录信息凭证
class UserCredential {
  @JsonKey(name: 'account', defaultValue: '')
  String account;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'password', defaultValue: '')
  String password;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'avatar')
  String? avatar;
  @<PERSON>son<PERSON><PERSON>(name: 'rememberPassword', defaultValue: false)
  bool rememberPassword;

  UserCredential({
    this.account = '',
    this.password = '',
    this.avatar,
    this.rememberPassword = false,
  });

  factory UserCredential.fromJson(Map<String, dynamic> json) {
    return _$UserCredentialFromJson(json);
  }

  Map<String, dynamic> toJson() => _$UserCredentialToJson(this);
}
