import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'enterprise_detail.g.dart';

@JsonSerializable()
// ignore: must_be_immutable
class EnterpriseDetail extends Equatable {
  @J<PERSON><PERSON><PERSON>(name: 'Id')
  String? id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'EnterpriseName', defaultValue: '')
  String enterpriseName;
  @<PERSON>son<PERSON><PERSON>(name: 'EnterpriseDescribe', defaultValue: '')
  String enterpriseDescribe;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'EnterpriseAvatarImage')
  String? enterpriseAvatarImage;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'EnterpriseAvatarImageId')
  String? enterpriseAvatarImageId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'EnterpriseClientImage')
  String? enterpriseClientImage;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'EnterpriseClientImageId')
  String? enterpriseClientImageId;

  EnterpriseDetail({
    this.id,
    this.enterpriseName = '',
    this.enterpriseDescribe = '',
    this.enterpriseAvatarImage,
    this.enterpriseAvatarImageId,
    this.enterpriseClientImage,
    this.enterpriseClientImageId,
  });

  factory EnterpriseDetail.fromJson(Map<String, dynamic> json) {
    return _$EnterpriseDetailFromJson(json);
  }

  Map<String, dynamic> toJson() => _$EnterpriseDetailToJson(this);

  @override
  List<Object?> get props => [
    enterpriseName,
    enterpriseDescribe,
    enterpriseAvatarImage,
    enterpriseAvatarImageId,
    enterpriseClientImage,
    enterpriseClientImageId,
  ];
}
