import 'package:flutter/material.dart';
import 'dart:async';
import 'package:jyt_components_package/jyt_components_package.dart';

/// 选择器组件
///
/// 提供类似Element UI的下拉选择器功能，支持单选和多选模式。
/// 支持搜索过滤、自定义渲染等功能。
class AppSelect<T> extends StatefulWidget {
  /// 导航器Key，可选参数。如果不提供，将自动从context获取
  final GlobalKey<NavigatorState>? navigatorKey;

  /// 选择器值（单选模式）
  final T? value;

  /// 选择器值列表（多选模式）
  final List<T>? values;

  /// 选项列表
  final List<SelectOption<T>> options;

  /// 控制器
  final SelectController<T>? controller;

  /// 标签文本
  final String? label;

  /// 占位文本
  final String placeholder;

  /// 是否禁用
  final bool disabled;

  /// 是否可清空
  final bool clearable;

  /// 是否多选
  final bool multiple;

  /// 是否可搜索
  final bool filterable;

  /// 远程搜索函数，设置后将启用远程搜索模式
  final Future<List<SelectOption<T>>> Function(String query)? remoteMethod;

  /// 是否在选项为空时禁用选择器
  final bool noDataText;

  /// 选项为空时显示的文字
  final String noMatchText;

  /// 多选时是否折叠选中值
  final bool collapseTags;

  /// 多选时折叠显示的选中值数量
  final int maxCollapseTagCount;

  /// 选择器尺寸
  final SelectSize size;

  /// 过滤方式
  final SelectFilterMode filterMode;

  /// 自定义过滤函数
  final bool Function(SelectOption<T> option, String query)? filterMethod;

  /// 下拉框宽度
  final double? popupWidth;

  /// 下拉框最大高度
  final double? popupMaxHeight;

  /// 选项高度
  final double? optionHeight;

  /// 是否默认展开
  final bool defaultOpen;

  /// 虚拟滚动，大量数据时启用
  final bool virtualScroll;

  /// 自定义选项渲染
  final Widget Function(BuildContext context, SelectOption<T> option, bool selected)? optionBuilder;

  /// 自定义选中标签渲染（多选模式）
  final Widget Function(BuildContext context, SelectOption<T> option, VoidCallback onRemove)?
  tagBuilder;

  /// 值变化回调
  final void Function(T? value)? onChanged;

  /// 值变化回调（多选模式）
  final void Function(List<T> values)? onMultiChanged;

  /// 获得焦点回调
  final void Function()? onFocus;

  /// 失去焦点回调
  final void Function()? onBlur;

  /// 展开回调
  final void Function()? onOpen;

  /// 收起回调
  final void Function()? onClose;

  /// 清空回调
  final void Function()? onClear;

  /// 鼠标移入回调
  final void Function()? onMouseEnter;

  /// 鼠标移出回调
  final void Function()? onMouseLeave;

  /// 选项选中回调
  final void Function(SelectOption<T> option)? onOptionSelected;

  /// 创建选择器
  const AppSelect({
    super.key,
    this.value,
    this.values,
    required this.options,
    this.controller,
    this.label,
    this.placeholder = '请选择',
    this.disabled = false,
    this.clearable = false,
    this.multiple = false,
    this.filterable = false,
    this.remoteMethod,
    this.noDataText = true,
    this.noMatchText = '无匹配数据',
    this.collapseTags = false,
    this.maxCollapseTagCount = 1,
    this.size = SelectSize.medium,
    this.filterMode = SelectFilterMode.contains,
    this.filterMethod,
    this.popupWidth,
    this.popupMaxHeight,
    this.optionHeight,
    this.defaultOpen = false,
    this.virtualScroll = false,
    this.optionBuilder,
    this.tagBuilder,
    this.onChanged,
    this.onMultiChanged,
    this.onFocus,
    this.onBlur,
    this.onOpen,
    this.onClose,
    this.onClear,
    this.onMouseEnter,
    this.onMouseLeave,
    this.onOptionSelected,
    this.navigatorKey,
  });

  @override
  State<AppSelect<T>> createState() => _AppSelectState<T>();
}

class _AppSelectState<T> extends State<AppSelect<T>> {
  /// 内部控制器
  late SelectController<T> _controller;

  /// 下拉面板控制器
  final PopupOverlayController _popupController = PopupOverlayController();

  /// 输入框控制器（搜索用）
  final TextEditingController _textEditingController = TextEditingController();

  /// 输入框焦点
  final FocusNode _focusNode = FocusNode();

  /// 搜索防抖计时器
  Timer? _searchDebounce;

  /// 选项高度
  double _optionHeight = 36;

  /// 是否鼠标悬浮
  bool _isHovered = false;

  /// 防止点击导致的焦点变化立即关闭面板
  bool _preventBlurClose = false;

  @override
  void initState() {
    super.initState();
    _initController();
    _focusNode.addListener(_handleFocusChange);

    // 默认展开
    if (widget.defaultOpen) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _popupController.show();
      });
    }
  }

  /// 初始化控制器
  void _initController() {
    // 初始化控制器
    _controller =
        widget.controller ??
        SelectController<T>(
          value: widget.value,
          multiValues: widget.values,
          multiple: widget.multiple,
          options: widget.options,
        );

    // 如果使用外部控制器，确保选项数据同步
    if (widget.controller != null) {
      _controller.setOptions(widget.options);
    }

    // 设置过滤模式
    if (widget.filterable) {
      if (widget.remoteMethod != null) {
        _controller.setFilterMode(SelectFilterMode.remote);
      } else {
        _controller.setFilterMode(widget.filterMode);
        if (widget.filterMethod != null) {
          _controller.setCustomFilterFn(widget.filterMethod!);
        }
      }
    }

    // 设置选项高度
    if (widget.optionHeight != null) {
      _optionHeight = widget.optionHeight!;
    }
  }

  @override
  void didUpdateWidget(AppSelect<T> oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 更新控制器
    if (widget.controller != oldWidget.controller && widget.controller != null) {
      _controller = widget.controller!;
    }

    // 更新选项列表
    if (widget.options != oldWidget.options) {
      _controller.setOptions(widget.options);
    }

    // 更新值
    if (widget.value != oldWidget.value && !widget.multiple) {
      _controller.setValue(widget.value);
    }

    // 更新多选值
    if (widget.values != oldWidget.values && widget.multiple) {
      _controller.setMultiValues(widget.values ?? []);
    }
  }

  @override
  void dispose() {
    // 取消搜索防抖计时器
    _searchDebounce?.cancel();

    // 如果是内部创建的控制器，需要释放
    if (widget.controller == null) {
      _controller.dispose();
    }
    _popupController.dispose();
    _textEditingController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// 处理焦点变化
  void _handleFocusChange() {
    if (_focusNode.hasFocus) {
      widget.onFocus?.call();
      // 展开下拉面板
      if (!_popupController.isVisible && !widget.disabled) {
        _popupController.show();
      }
    } else {
      widget.onBlur?.call();
      // 隐藏下拉面板（如果点击导致了面板显示，不处理）
      if (_popupController.isVisible && !_preventBlurClose) {
        _popupController.hide();
      }
      _preventBlurClose = false;
    }
  }

  /// 计算下拉框宽度
  double _calculatePopupWidth() {
    if (widget.popupWidth != null) return widget.popupWidth!;
    return context.findRenderObject()?.paintBounds.width ?? 200;
  }

  /// 处理选项点击
  void _handleOptionSelected(SelectOption<T> option) {
    if (option.disabled) return;

    if (widget.multiple) {
      _controller.toggleValue(option.value);
      // 多选模式下不关闭弹窗，同时触发回调
      widget.onMultiChanged?.call(_controller.multiValues);
    } else {
      _controller.setValue(option.value);
      // 单选模式选择后关闭弹窗
      _popupController.hide();
      // 触发回调
      widget.onChanged?.call(option.value);
    }

    // 触发选项选中回调
    widget.onOptionSelected?.call(option);

    // 搜索模式下，选择后清空搜索词
    if (widget.filterable) {
      _textEditingController.clear();
      _controller.setSearchText('');
    }
  }

  /// 清空选择
  void _handleClear() {
    _controller.clear();
    if (widget.multiple) {
      widget.onMultiChanged?.call([]);
    } else {
      widget.onChanged?.call(null);
    }
    widget.onClear?.call();
  }

  /// 处理标签移除
  void _handleRemoveTag(T value) {
    _controller.removeValue(value);
    widget.onMultiChanged?.call(_controller.multiValues);
  }

  /// 处理搜索输入
  void _handleSearch(String value) {
    // 如果搜索值与当前控制器中的搜索值相同，则不处理
    if (value == _controller.searchText) return;

    // 取消之前的延迟搜索
    _searchDebounce?.cancel();

    // 设置延迟搜索（防抖）
    _searchDebounce = Timer(const Duration(milliseconds: 500), () {
      if (widget.remoteMethod != null) {
        // 远程搜索
        _controller.setSearchText(value);
        _controller.remoteSearch(widget.remoteMethod!);
      } else {
        // 本地搜索
        _controller.setSearchText(value);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AbsorbPointer(
      absorbing: widget.disabled,
      child: PopupOverlay(
        navigatorKey: widget.navigatorKey,
        controller: _popupController,
        position: PopupPosition.bottom,
        constraints: BoxConstraints(
          maxWidth: _calculatePopupWidth(),
          maxHeight: widget.popupMaxHeight ?? 300,
        ),
        offset: const Offset(0, 5),
        closeOnTapOutside: true,
        onClose: () {
          // 强制刷新UI状态
          if (mounted) setState(() {});
        },
        child: _buildSelector(context),
        builder: (context, closeOverlay) {
          return _buildDropdown();
        },
      ),
    );
  }

  /// 构建选择器输入框
  Widget _buildSelector(BuildContext context) {
    final double fontSize = SelectStyle.getFontSize(widget.size);
    final bool isFocused = _focusNode.hasFocus || _popupController.isVisible;

    // 是否显示清空按钮
    final bool showClearbleBtn =
        widget.clearable && _controller.hasValue && _isHovered && !widget.disabled;

    return MouseRegion(
      onEnter: (_) {
        setState(() => _isHovered = true);
        widget.onMouseEnter?.call();
      },
      onExit: (_) {
        setState(() => _isHovered = false);
        widget.onMouseLeave?.call();
      },
      child: GestureDetector(
        onTap: widget.disabled ? null : _toggleDropdown,
        child: Container(
          height: SelectStyle.getSelectorHeight(widget.size),
          decoration: BoxDecoration(
            border: Border.all(
              color: SelectStyle.getBorderColor(
                context,
                isFocused: isFocused,
                disabled: widget.disabled,
              ),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
            color: SelectStyle.getBackgroundColor(context, disabled: widget.disabled),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Row(
            children: [
              // 标签（如果有）
              if (widget.label != null) ...[
                Text(widget.label!, style: SelectStyle.getLabelStyle(context, fontSize: fontSize)),
                const SizedBox(width: 8),
              ],

              // 使用 ListenableBuilder 包装依赖控制器状态的部分
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(right: 5),
                  child: ListenableBuilder(
                    listenable: _controller,
                    builder: (context, _) {
                      // 多选模式下的标签列表
                      if (widget.multiple && _controller.hasValue) {
                        return SelectTagList<T>(
                          selectedOptions: _controller.selectedOptions,
                          onRemoveTag: _handleRemoveTag,
                          collapseTags: widget.collapseTags,
                          maxCollapseTagCount: widget.maxCollapseTagCount,
                          disabled: widget.disabled,
                          fontSize: fontSize,
                          tagBuilder: widget.tagBuilder,
                        );
                      }
                      // 单选模式下的选中值
                      else if (!widget.multiple && _controller.hasValue) {
                        final selectedOption = _controller.selectedOption;
                        if (selectedOption == null) return const SizedBox();

                        return Text(
                          selectedOption.label,
                          style: SelectStyle.getValueStyle(
                            context,
                            fontSize: fontSize,
                            disabled: widget.disabled,
                          ),
                          overflow: TextOverflow.ellipsis,
                        );
                      }
                      // 无选中值时显示占位符
                      else {
                        return Text(
                          widget.placeholder,
                          style: SelectStyle.getPlaceholderStyle(
                            context,
                            fontSize: fontSize,
                            disabled: widget.disabled,
                          ),
                          overflow: TextOverflow.ellipsis,
                        );
                      }
                    },
                  ),
                ),
              ),

              // 右侧操作图标
              ListenableBuilder(
                listenable: _controller,
                builder: (context, _) {
                  return Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 清空按钮
                      if (showClearbleBtn)
                        AppButton(
                          type: ButtonType.transparent,
                          size: ButtonSize.small,
                          iconData: Icons.clear,
                          color: context.icon200,
                          onPressed: _handleClear,
                        ),

                      // 下拉箭头图标
                      Icon(
                        _popupController.isVisible ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                        color: widget.disabled
                            ? Theme.of(context).disabledColor
                            : context.textSecondary,
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建下拉选项列表
  Widget _buildDropdown() {
    return ListenableBuilder(
      listenable: _controller,
      builder: (context, _) {
        return SelectDropdown<T>(
          options: _controller.filteredOptions,
          isOptionSelected: (option) => _isOptionSelected(option),
          onOptionSelected: _handleOptionSelected,
          dropdownWidth: _calculatePopupWidth(),
          optionHeight: _optionHeight,
          filterable: widget.filterable,
          onSearch: _handleSearch,
          searchController: _textEditingController,
          popupMaxHeight: widget.popupMaxHeight,
          virtualScroll: widget.virtualScroll,
          optionBuilder: widget.optionBuilder,
          multiple: widget.multiple,
          isLoadingRemoteData: _controller.isLoadingRemoteData,
          noMatchText: widget.noMatchText,
        );
      },
    );
  }

  /// 切换下拉面板显示状态
  void _toggleDropdown() {
    if (!_focusNode.hasFocus) {
      _focusNode.requestFocus();
    }
    _preventBlurClose = true;
    if (_popupController.isVisible) {
      _popupController.hide();
      widget.onClose?.call();
    } else {
      _popupController.show();
      widget.onOpen?.call();

      // 如果启用了远程搜索，在下拉框打开时立即执行一次搜索
      if (widget.filterable && widget.remoteMethod != null) {
        _controller.remoteSearch(widget.remoteMethod!);
      }
    }
    // 强制刷新UI状态
    if (mounted) setState(() {});
  }

  /// 检查选项是否被选中
  bool _isOptionSelected(SelectOption<T> option) {
    if (widget.multiple) {
      return _controller.multiValues.contains(option.value);
    } else {
      return _controller.value == option.value;
    }
  }
}
