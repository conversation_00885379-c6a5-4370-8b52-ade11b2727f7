// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';

class IconFont {
  static const String fontFamily = 'IconFont';

  // 自动生成的iconfont常量
  static const IconData mianxing_wushuju = IconData(0xe937, fontFamily: fontFamily);
  static const IconData mianxing_tuding = IconData(0xe938, fontFamily: fontFamily);
  static const IconData mianxing_tongzhi = IconData(0xe939, fontFamily: fontFamily);
  static const IconData yigouxuan_xiao = IconData(0xe931, fontFamily: fontFamily);
  static const IconData zimushunxu = IconData(0xe932, fontFamily: fontFamily);
  static const IconData zhankaiquanbu = IconData(0xe933, fontFamily: fontFamily);
  static const IconData ziji = IconData(0xe934, fontFamily: fontFamily);
  static const IconData zimudaoxu = IconData(0xe935, fontFamily: fontFamily);
  static const IconData yigouxuan = IconData(0xe936, fontFamily: fontFamily);
  static const IconData wushuju = IconData(0xe926, fontFamily: fontFamily);
  static const IconData xiezuobianji = IconData(0xe927, fontFamily: fontFamily);
  static const IconData xianxing_tuding = IconData(0xe928, fontFamily: fontFamily);
  static const IconData yaoqingtianjia = IconData(0xe929, fontFamily: fontFamily);
  static const IconData yijianzhuanhua = IconData(0xe92a, fontFamily: fontFamily);
  static const IconData xiangzuoshouqi = IconData(0xe92b, fontFamily: fontFamily);
  static const IconData tianjiayonghu = IconData(0xe92c, fontFamily: fontFamily);
  static const IconData weiguishuxiang = IconData(0xe92d, fontFamily: fontFamily);
  static const IconData tongzhi = IconData(0xe92e, fontFamily: fontFamily);
  static const IconData xuanzhong = IconData(0xe92f, fontFamily: fontFamily);
  static const IconData xiezuozheguangbiao = IconData(0xe930, fontFamily: fontFamily);
  static const IconData tianjiatupian = IconData(0xe921, fontFamily: fontFamily);
  static const IconData tianjiarenwucaidan = IconData(0xe922, fontFamily: fontFamily);
  static const IconData shoucang = IconData(0xe923, fontFamily: fontFamily);
  static const IconData suoding = IconData(0xe924, fontFamily: fontFamily);
  static const IconData shanchu = IconData(0xe925, fontFamily: fontFamily);
  static const IconData shouqiquanbu = IconData(0xe920, fontFamily: fontFamily);
  static const IconData pinglun = IconData(0xe91b, fontFamily: fontFamily);
  static const IconData pinglunliebiao = IconData(0xe91c, fontFamily: fontFamily);
  static const IconData jianqie = IconData(0xe91d, fontFamily: fontFamily);
  static const IconData quanxian = IconData(0xe91e, fontFamily: fontFamily);
  static const IconData kapianshezhi = IconData(0xe91f, fontFamily: fontFamily);
  static const IconData fuzhi = IconData(0xe914, fontFamily: fontFamily);
  static const IconData duankaiguanlian = IconData(0xe915, fontFamily: fontFamily);
  static const IconData fenzu = IconData(0xe916, fontFamily: fontFamily);
  static const IconData fenping = IconData(0xe917, fontFamily: fontFamily);
  static const IconData dongzuojilu = IconData(0xe918, fontFamily: fontFamily);
  static const IconData jianchaxiang = IconData(0xe919, fontFamily: fontFamily);
  static const IconData fasong = IconData(0xe91a, fontFamily: fontFamily);
  static const IconData a_ = IconData(0xe913, fontFamily: fontFamily);
  static const IconData dongtaizhuanhua = IconData(0xe910, fontFamily: fontFamily);
  static const IconData biaoqian = IconData(0xe911, fontFamily: fontFamily);
  static const IconData dagang = IconData(0xe912, fontFamily: fontFamily);
  static const IconData xianxing_bukejian = IconData(0xe90e, fontFamily: fontFamily);
  static const IconData xianxing_kejian = IconData(0xe90f, fontFamily: fontFamily);
  static const IconData mianxing_gongzuobao_WP = IconData(0xe90d, fontFamily: fontFamily);
  static const IconData xianxing_jianshao_yuan = IconData(0xe8f0, fontFamily: fontFamily);
  static const IconData xianxing_dianxuan = IconData(0xe8f1, fontFamily: fontFamily);
  static const IconData xianxing_fengxiandian = IconData(0xe8f2, fontFamily: fontFamily);
  static const IconData xianxing_ditu = IconData(0xe8f3, fontFamily: fontFamily);
  static const IconData xianxing_kuansongxinghanggao = IconData(0xe8f4, fontFamily: fontFamily);
  static const IconData xianxing_liebiaomoshi = IconData(0xe8f5, fontFamily: fontFamily);
  static const IconData xianxing_jiaofujian = IconData(0xe8f6, fontFamily: fontFamily);
  static const IconData xianxing_siweidaotumoshi = IconData(0xe8f7, fontFamily: fontFamily);
  static const IconData xianxing_pinglunliebiao = IconData(0xe8f8, fontFamily: fontFamily);
  static const IconData xianxing_waibucaigou = IconData(0xe8f9, fontFamily: fontFamily);
  static const IconData xianxing_weiguanlianxiang = IconData(0xe8fa, fontFamily: fontFamily);
  static const IconData xianxing_xiangshangyibuchehui = IconData(0xe8fb, fontFamily: fontFamily);
  static const IconData xianxing_xinxiweiwanshan = IconData(0xe8fc, fontFamily: fontFamily);
  static const IconData xianxing_rili1 = IconData(0xe8fd, fontFamily: fontFamily);
  static const IconData mianxing_fuzhi = IconData(0xe8fe, fontFamily: fontFamily);
  static const IconData xianxing_chixushijian = IconData(0xe8ff, fontFamily: fontFamily);
  static const IconData xianxing_jincouxinghanggao = IconData(0xe900, fontFamily: fontFamily);
  static const IconData mianxing_putongzujian_WE = IconData(0xe901, fontFamily: fontFamily);
  static const IconData xianxing_shizhongxinghanggao = IconData(0xe902, fontFamily: fontFamily);
  static const IconData xianxing_shouxing = IconData(0xe903, fontFamily: fontFamily);
  static const IconData mianxing_jimirenwu = IconData(0xe904, fontFamily: fontFamily);
  static const IconData xianxing_xiangxiayibuchehui = IconData(0xe905, fontFamily: fontFamily);
  static const IconData xianxing_xiajirenwutixing = IconData(0xe906, fontFamily: fontFamily);
  static const IconData mianxing_kongzhizhanghu_CA = IconData(0xe907, fontFamily: fontFamily);
  static const IconData mianxing_shubiaojiantou = IconData(0xe908, fontFamily: fontFamily);
  static const IconData mianxing_suoding = IconData(0xe909, fontFamily: fontFamily);
  static const IconData mianxing_jiaofuwu = IconData(0xe90a, fontFamily: fontFamily);
  static const IconData xianxing_xuqiuguanli = IconData(0xe90b, fontFamily: fontFamily);
  static const IconData mianxing_guihuabao_PP = IconData(0xe90c, fontFamily: fontFamily);
  static const IconData mianxing_shitu_kapian = IconData(0xe8ed, fontFamily: fontFamily);
  static const IconData mianxing_rili = IconData(0xe8ee, fontFamily: fontFamily);
  static const IconData mianxing_qiyegaikuang = IconData(0xe89a, fontFamily: fontFamily);
  static const IconData mianxing_qiyeguanli = IconData(0xe8ef, fontFamily: fontFamily);
  static const IconData mianxing_xietong = IconData(0xe8dc, fontFamily: fontFamily);
  static const IconData mianxing_xuexirenwu = IconData(0xe8dd, fontFamily: fontFamily);
  static const IconData mianxing_xiangmushezhi = IconData(0xe8de, fontFamily: fontFamily);
  static const IconData mianxing_tongxunlu = IconData(0xe8df, fontFamily: fontFamily);
  static const IconData mianxing_tongyongrenwu = IconData(0xe8e0, fontFamily: fontFamily);
  static const IconData mianxing_xiangmuchengyuanshezhi = IconData(0xe8e1, fontFamily: fontFamily);
  static const IconData mianxing_xiangmu = IconData(0xe8e2, fontFamily: fontFamily);
  static const IconData mianxing_shuoming = IconData(0xe89b, fontFamily: fontFamily);
  static const IconData mianxing_wenjianjia = IconData(0xe8e3, fontFamily: fontFamily);
  static const IconData mianxing_tianjia = IconData(0xe8e4, fontFamily: fontFamily);
  static const IconData mianxing_shengchanrenwu = IconData(0xe8e5, fontFamily: fontFamily);
  static const IconData mianxing_shitu_biaoge = IconData(0xe8e6, fontFamily: fontFamily);
  static const IconData mianxing_shanchu = IconData(0xe8e7, fontFamily: fontFamily);
  static const IconData mianxing_shoucang = IconData(0xe8e8, fontFamily: fontFamily);
  static const IconData mianxing_xiala = IconData(0xe8e9, fontFamily: fontFamily);
  static const IconData mianxing_shishirenwu = IconData(0xe8ea, fontFamily: fontFamily);
  static const IconData mianxing_tixing = IconData(0xe8eb, fontFamily: fontFamily);
  static const IconData mianxing_renshihangzheng = IconData(0xe8ec, fontFamily: fontFamily);
  static const IconData xianxing_qiyebumen = IconData(0xe8cc, fontFamily: fontFamily);
  static const IconData xianxing_liuchengjiedian = IconData(0xe8cd, fontFamily: fontFamily);
  static const IconData xianxing_jimi = IconData(0xe8ce, fontFamily: fontFamily);
  static const IconData xianxing_gudingdianhua = IconData(0xe8cf, fontFamily: fontFamily);
  static const IconData xianxing_duihua = IconData(0xe8d0, fontFamily: fontFamily);
  static const IconData xianxing_chakanxiangguanhangdongxiang = IconData(0xe8d1, fontFamily: fontFamily);
  static const IconData xianxing_danjiantou_zuo = IconData(0xe8d2, fontFamily: fontFamily);
  static const IconData xianxing_guanbi = IconData(0xe8d3, fontFamily: fontFamily);
  static const IconData mianxing_zhankai = IconData(0xe8d4, fontFamily: fontFamily);
  static const IconData xianxing_chuangjian = IconData(0xe8d5, fontFamily: fontFamily);
  static const IconData xianxing_danjiantou_you = IconData(0xe8d6, fontFamily: fontFamily);
  static const IconData xianxing_caidanzhedie = IconData(0xe8d7, fontFamily: fontFamily);
  static const IconData mianxing_yanfarenwu = IconData(0xe89d, fontFamily: fontFamily);
  static const IconData xianxing_biaoqing = IconData(0xe8d8, fontFamily: fontFamily);
  static const IconData mianxing_yiwancheng = IconData(0xe8d9, fontFamily: fontFamily);
  static const IconData mianxing_xitongshezhi = IconData(0xe89c, fontFamily: fontFamily);
  static const IconData mianxing_yunhangzhong = IconData(0xe8da, fontFamily: fontFamily);
  static const IconData mianxing_xiaoxi = IconData(0xe8db, fontFamily: fontFamily);
  static const IconData xianxing_gexingzhuti = IconData(0xe8c8, fontFamily: fontFamily);
  static const IconData xianxing_shaixuan = IconData(0xe8c9, fontFamily: fontFamily);
  static const IconData xianxing_pingshen = IconData(0xe8ca, fontFamily: fontFamily);
  static const IconData xianxing_jianshao = IconData(0xe8cb, fontFamily: fontFamily);
  static const IconData xianxing_bianji = IconData(0xe89e, fontFamily: fontFamily);
  static const IconData xianxing_shenqingkehuweihuquanxian = IconData(0xe8bc, fontFamily: fontFamily);
  static const IconData xianxing_shoujihaoma = IconData(0xe8bd, fontFamily: fontFamily);
  static const IconData xianxing_quanping_tuichu = IconData(0xe8be, fontFamily: fontFamily);
  static const IconData xianxing_gengduo = IconData(0xe89f, fontFamily: fontFamily);
  static const IconData xianxing_shuangjiantou_you = IconData(0xe8bf, fontFamily: fontFamily);
  static const IconData xianxing_shuangjiantou_zuo = IconData(0xe8c0, fontFamily: fontFamily);
  static const IconData xianxing_mingpian = IconData(0xe8c1, fontFamily: fontFamily);
  static const IconData xianxing_shuaxin = IconData(0xe8c2, fontFamily: fontFamily);
  static const IconData xianxing_shangchuan = IconData(0xe8c3, fontFamily: fontFamily);
  static const IconData xianxing_rili = IconData(0xe8c4, fontFamily: fontFamily);
  static const IconData xianxing_quanping_kaiqi = IconData(0xe8c5, fontFamily: fontFamily);
  static const IconData xianxing_qiyeguanlihoutai = IconData(0xe8c6, fontFamily: fontFamily);
  static const IconData xianxing_jiemi = IconData(0xe8c7, fontFamily: fontFamily);
  static const IconData xianxing_tixing = IconData(0xe8a2, fontFamily: fontFamily);
  static const IconData mianxing_guanbi = IconData(0xe8ac, fontFamily: fontFamily);
  static const IconData mianxing_duihua = IconData(0xe8ad, fontFamily: fontFamily);
  static const IconData mianxing_biaogepeizhi = IconData(0xe8ae, fontFamily: fontFamily);
  static const IconData xianxing_youxiang = IconData(0xe8af, fontFamily: fontFamily);
  static const IconData xianxing_zhongyaochengdu = IconData(0xe8b0, fontFamily: fontFamily);
  static const IconData xiangqing_kuozhandakai = IconData(0xe8b1, fontFamily: fontFamily);
  static const IconData xianxing_xitongshezhi = IconData(0xe8b2, fontFamily: fontFamily);
  static const IconData xianxing_zuidahua = IconData(0xe8b3, fontFamily: fontFamily);
  static const IconData xianxing_mubiao = IconData(0xe8a0, fontFamily: fontFamily);
  static const IconData xianxing_sousuo = IconData(0xe8b4, fontFamily: fontFamily);
  static const IconData xianxing_zuixiaohua = IconData(0xe8b5, fontFamily: fontFamily);
  static const IconData xianxing_ziduanpeizhi = IconData(0xe8b6, fontFamily: fontFamily);
  static const IconData xianxing_xiangqingdakai = IconData(0xe8b7, fontFamily: fontFamily);
  static const IconData xianxing_xiala = IconData(0xe8b8, fontFamily: fontFamily);
  static const IconData xianxing_tuodongpaixu = IconData(0xe8b9, fontFamily: fontFamily);
  static const IconData xianxing_tuichudenglu = IconData(0xe8ba, fontFamily: fontFamily);
  static const IconData xianxing_shijian = IconData(0xe8a1, fontFamily: fontFamily);
  static const IconData xianxing_tianjia = IconData(0xe8bb, fontFamily: fontFamily);
  static const IconData mianxing_pingshen = IconData(0xe8a5, fontFamily: fontFamily);
  static const IconData mianxing_gengduo = IconData(0xe8a4, fontFamily: fontFamily);
  static const IconData mianxing_lianjie = IconData(0xe8a6, fontFamily: fontFamily);
  static const IconData mianxing_jianshao = IconData(0xe8a7, fontFamily: fontFamily);
  static const IconData mianxing_lichengbei = IconData(0xe8a8, fontFamily: fontFamily);
  static const IconData xianxing_zhanghaoshezhi = IconData(0xe8a3, fontFamily: fontFamily);
  static const IconData mianxing_huibao = IconData(0xe8a9, fontFamily: fontFamily);
  static const IconData mianxing_gongzuotai = IconData(0xe8aa, fontFamily: fontFamily);
  static const IconData mianxing_jieduan = IconData(0xe8ab, fontFamily: fontFamily);
}
