import 'package:jyt_components_package/utils/app_util.dart';

/// 平台字体工具类
class AppFont {
  /// 系统字体
  static String get platformFont {
    // 处理Web平台
    if (!AppUtil.isDesktop) {
      return 'SourceHanSans';
    }

    if (AppUtil.isMacOS) {
      return 'PingFang SC';
    } else {
      return 'SourceHanSans';
    }
  }

  /// 通用字体样式获取方法
  static String getFontForPlatform() {
    return platformFont;
  }
}
