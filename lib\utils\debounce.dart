import 'dart:async';

/// 防抖工具类
class Debounce {
  final Duration delay;
  Timer? _timer;

  Debounce({this.delay = const Duration(milliseconds: 500)});

  /// 执行防抖函数
  void run(void Function() action) {
    _timer?.cancel(); // 取消前一个定时器
    _timer = Timer(delay, action); // 创建新的定时器
  }

  /// 释放资源（在页面dispose时调用）
  void dispose() {
    _timer?.cancel();
    _timer = null;
  }
}

/// 快捷调用方式：返回一个防抖处理函数
Function() debounce(
  void Function() func, [
  Duration delay = const Duration(milliseconds: 500),
]) {
  Timer? timer;
  return () {
    timer?.cancel();
    timer = Timer(delay, func);
  };
}
