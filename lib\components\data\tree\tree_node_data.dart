/// 通用树节点数据接口
/// 任何想要在树组件中使用的数据类型都需要实现这个接口
abstract class TreeNodeData {
  /// 节点的唯一标识符
  String? get id;

  /// 节点名称
  String get name;

  /// 父节点ID
  String? get parentId;

  /// 父节点ID列表（用于计算层级深度）
  List<String> get parentIdList;
}

/// 通用树节点模型
/// 包装任何实现了 TreeNodeData 接口的数据
class AppTreeNode<T extends TreeNodeData> {
  /// 节点数据
  final T data;

  /// 子节点列表
  final List<AppTreeNode<T>> children;

  /// 是否展开
  bool isExpanded;

  /// 是否被选中（高亮显示）
  bool isSelected;

  /// 复选框是否选中
  bool isChecked;

  /// 复选框是否半选状态
  bool isIndeterminate;

  /// 搜索时节点是否可见
  bool isVisible;

  // 缓存计算结果以提高性能
  int? _cachedLevel;
  int? _cachedCheckedCount;

  AppTreeNode({
    required this.data,
    List<AppTreeNode<T>>? children,
    this.isExpanded = false,
    this.isSelected = false,
    this.isChecked = false,
    this.isIndeterminate = false,
    this.isVisible = true,
  }) : children = children ?? [];

  /// 检查是否为叶子节点
  bool get isLeaf => children.isEmpty;

  /// 获取节点层级深度（带缓存）
  int get level {
    _cachedLevel ??= data.parentIdList.length;
    return _cachedLevel!;
  }

  /// 清除缓存
  void clearCache() {
    _cachedLevel = null;
    _cachedCheckedCount = null;
  }

  /// 获取选中的子节点数量（带缓存）
  int get checkedChildrenCount {
    if (_cachedCheckedCount != null) {
      return _cachedCheckedCount!;
    }

    int count = 0;
    for (final child in children) {
      if (child.isChecked) {
        count++;
      }
    }

    _cachedCheckedCount = count;
    return count;
  }

  /// 检查是否有部分子节点被选中
  bool get hasPartiallyCheckedChildren {
    if (children.isEmpty) return false;

    bool hasChecked = false;
    bool hasUnchecked = false;

    for (final child in children) {
      if (child.isChecked || child.isIndeterminate) {
        hasChecked = true;
      } else {
        hasUnchecked = true;
      }

      // 如果既有选中又有未选中的，直接返回true
      if (hasChecked && hasUnchecked) {
        return true;
      }
    }

    return false;
  }

  /// 检查是否所有子节点都被选中
  bool get areAllChildrenChecked {
    if (children.isEmpty) return false;

    for (final child in children) {
      if (!child.isChecked) {
        return false;
      }
    }

    return true;
  }

  /// 递归查找节点
  AppTreeNode<T>? findNodeById(String nodeId) {
    if (data.id == nodeId) {
      return this;
    }

    for (final child in children) {
      final found = child.findNodeById(nodeId);
      if (found != null) {
        return found;
      }
    }

    return null;
  }

  /// 递归收集所有子节点
  List<AppTreeNode<T>> getAllDescendants() {
    final List<AppTreeNode<T>> descendants = [];

    for (final child in children) {
      descendants.add(child);
      descendants.addAll(child.getAllDescendants());
    }

    return descendants;
  }

  /// 递归收集所有选中的节点
  List<AppTreeNode<T>> getCheckedNodes() {
    final List<AppTreeNode<T>> checkedNodes = [];

    if (isChecked) {
      checkedNodes.add(this);
    }

    for (final child in children) {
      checkedNodes.addAll(child.getCheckedNodes());
    }

    return checkedNodes;
  }
}
