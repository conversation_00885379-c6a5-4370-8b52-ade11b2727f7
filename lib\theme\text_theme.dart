import 'package:flutter/material.dart';
import 'package:jyt_components_package/theme/colors.dart';
import 'package:jyt_components_package/theme/font.dart';

/// 文本主题
class AppTextTheme {
  /// 亮色主题文本样式
  static TextTheme light() {
    return TextTheme(
      // 大标题
      displayLarge: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textPrimary,
        fontSize: 24,
        fontWeight: FontWeight.bold,
      ),
      displayMedium: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textPrimary,
        fontSize: 22,
        fontWeight: FontWeight.bold,
      ),
      displaySmall: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textPrimary,
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),

      // 普通标题
      headlineLarge: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textPrimary,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
      headlineMedium: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textPrimary,
        fontSize: 16,
        fontWeight: FontWeight.bold,
      ),
      headlineSmall: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textPrimary,
        fontSize: 14,
        fontWeight: FontWeight.bold,
      ),

      // 正文样式
      bodyLarge: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textPrimary,
        fontSize: 16,
      ),
      bodyMedium: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textPrimary,
        fontSize: 14,
      ),
      bodySmall: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textSecondary,
        fontSize: 12,
      ),

      // 标签文本
      labelLarge: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textPrimary,
        fontSize: 14,
        fontWeight: FontWeight.normal,
      ),
      labelMedium: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textSecondary,
        fontSize: 12,
        fontWeight: FontWeight.normal,
      ),
      labelSmall: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textSecondary,
        fontSize: 10,
        fontWeight: FontWeight.normal,
      ),
    );
  }

  /// 暗色主题文本样式
  static TextTheme dark() {
    return TextTheme(
      // 大标题
      displayLarge: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textPrimaryDark,
        fontSize: 24,
        fontWeight: FontWeight.bold,
      ),
      displayMedium: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textPrimaryDark,
        fontSize: 22,
        fontWeight: FontWeight.bold,
      ),
      displaySmall: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textPrimaryDark,
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),

      // 普通标题
      headlineLarge: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textPrimaryDark,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
      headlineMedium: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textPrimaryDark,
        fontSize: 16,
        fontWeight: FontWeight.bold,
      ),
      headlineSmall: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textPrimaryDark,
        fontSize: 14,
        fontWeight: FontWeight.bold,
      ),

      // 正文样式
      bodyLarge: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textPrimaryDark,
        fontSize: 16,
      ),
      bodyMedium: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textPrimaryDark,
        fontSize: 14,
      ),
      bodySmall: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textPrimaryDark,
        fontSize: 12,
      ),

      // 标签文本
      labelLarge: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textSecondaryDark,
        fontSize: 14,
        fontWeight: FontWeight.normal,
      ),
      labelMedium: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textSecondaryDark,
        fontSize: 12,
        fontWeight: FontWeight.normal,
      ),
      labelSmall: TextStyle(
        fontFamily: AppFont.platformFont,
        color: AppColors.textSecondaryDark,
        fontSize: 10,
        fontWeight: FontWeight.normal,
      ),
    );
  }
}
