import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';

/// 自定义Switch组件，支持缩放功能
class AppSwitch extends StatelessWidget {
  /// 创建一个AppSwitch
  ///
  /// [scale]参数用于控制组件的缩放比例，默认为0.7
  const AppSwitch({
    super.key,
    required this.value,
    required this.onChanged,
    this.activeColor,
    this.activeTrackColor,
    this.inactiveThumbColor,
    this.inactiveTrackColor,
    this.activeThumbImage,
    this.onActiveThumbImageError,
    this.inactiveThumbImage,
    this.onInactiveThumbImageError,
    this.thumbColor,
    this.trackColor,
    this.trackOutlineColor,
    this.trackOutlineWidth,
    this.thumbIcon,
    this.materialTapTargetSize,
    this.dragStartBehavior = DragStartBehavior.start,
    this.mouseCursor,
    this.focusColor,
    this.hoverColor,
    this.overlayColor,
    this.splashRadius,
    this.focusNode,
    this.onFocusChange,
    this.autofocus = false,
    this.padding,
    this.scale = 0.7,
  }) : assert(activeThumbImage != null || onActiveThumbImageError == null),
       assert(inactiveThumbImage != null || onInactiveThumbImageError == null);

  /// 开关当前值
  final bool value;

  /// 值变化的回调函数
  final ValueChanged<bool>? onChanged;

  /// 活动状态的颜色
  final Color? activeColor;

  /// 活动状态的轨道颜色
  final Color? activeTrackColor;

  /// 非活动状态的拇指颜色
  final Color? inactiveThumbColor;

  /// 非活动状态的轨道颜色
  final Color? inactiveTrackColor;

  /// 活动状态的拇指图片
  final ImageProvider? activeThumbImage;

  /// 活动状态拇指图片错误回调
  final ImageErrorListener? onActiveThumbImageError;

  /// 非活动状态的拇指图片
  final ImageProvider? inactiveThumbImage;

  /// 非活动状态拇指图片错误回调
  final ImageErrorListener? onInactiveThumbImageError;

  /// 拇指颜色
  final MaterialStateProperty<Color?>? thumbColor;

  /// 轨道颜色
  final MaterialStateProperty<Color?>? trackColor;

  /// 轨道轮廓颜色
  final MaterialStateProperty<Color?>? trackOutlineColor;

  /// 轨道轮廓宽度
  final MaterialStateProperty<double?>? trackOutlineWidth;

  /// 拇指图标
  final MaterialStateProperty<Icon?>? thumbIcon;

  /// Material点击目标大小
  final MaterialTapTargetSize? materialTapTargetSize;

  /// 拖动开始行为
  final DragStartBehavior dragStartBehavior;

  /// 鼠标光标
  final MouseCursor? mouseCursor;

  /// 聚焦颜色
  final Color? focusColor;

  /// 悬停颜色
  final Color? hoverColor;

  /// 覆盖颜色
  final MaterialStateProperty<Color?>? overlayColor;

  /// 溅射半径
  final double? splashRadius;

  /// 焦点节点
  final FocusNode? focusNode;

  /// 焦点变化回调
  final ValueChanged<bool>? onFocusChange;

  /// 自动聚焦
  final bool autofocus;

  /// 内边距
  final EdgeInsetsGeometry? padding;

  /// 缩放比例，默认0.7
  final double scale;

  @override
  Widget build(BuildContext context) {
    return Transform.scale(
      scale: scale,
      child: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: activeColor,
        activeTrackColor: activeTrackColor,
        inactiveThumbColor: inactiveThumbColor,
        inactiveTrackColor: inactiveTrackColor,
        activeThumbImage: activeThumbImage,
        onActiveThumbImageError: onActiveThumbImageError,
        inactiveThumbImage: inactiveThumbImage,
        onInactiveThumbImageError: onInactiveThumbImageError,
        thumbColor: thumbColor,
        trackColor: trackColor,
        trackOutlineColor: trackOutlineColor,
        trackOutlineWidth: trackOutlineWidth,
        thumbIcon: thumbIcon,
        materialTapTargetSize: materialTapTargetSize,
        dragStartBehavior: dragStartBehavior,
        mouseCursor: mouseCursor,
        focusColor: focusColor,
        hoverColor: hoverColor,
        overlayColor: overlayColor,
        splashRadius: splashRadius,
        focusNode: focusNode,
        onFocusChange: onFocusChange,
        autofocus: autofocus,
        padding: padding,
      ),
    );
  }
}
