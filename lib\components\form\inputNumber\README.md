# AppInputNumber 组件

数字输入计数器组件，支持按钮点击和手动输入两种方式。

## 功能特性

- ✅ 支持按钮点击和手动输入两种方式
- ✅ 可配置最小值、最大值和步长
- ✅ 自动禁用超出范围的按钮
- ✅ 支持禁用状态和自定义样式
- ✅ 包含输入验证和格式化
- ✅ **新增：支持四种不同尺寸配置**

## 尺寸配置

组件现在支持四种不同的尺寸选项：

### InputNumberSize 枚举

```dart
enum InputNumberSize {
  large,   // 大尺寸 - 适用于重要的数值输入场景
  medium,  // 中等尺寸 - 默认尺寸，适用于大多数场景
  small,   // 小尺寸 - 适用于紧凑布局
  mini,    // 迷你尺寸 - 适用于极度紧凑的空间
}
```

### 尺寸规格

| 尺寸 | 高度 | 字体大小 | 按钮宽度 | 图标大小 | 内边距 |
|------|------|----------|----------|----------|--------|
| Large | 48px | 16px | 36px | 18px | 12px |
| Medium | 40px | 14px | 32px | 16px | 8px |
| Small | 32px | 12px | 28px | 14px | 6px |
| Mini | 24px | 11px | 20px | 12px | 4px |

## 基本用法

### 默认尺寸（Medium）

```dart
AppInputNumber(
  value: 10,
  onChanged: (value) => print('新值: $value'),
  min: 0,
  max: 100,
  step: 1,
)
```

### 指定尺寸

```dart
// 大尺寸
AppInputNumber(
  value: 10,
  size: InputNumberSize.large,
  onChanged: (value) => print('新值: $value'),
)

// 小尺寸
AppInputNumber(
  value: 10,
  size: InputNumberSize.small,
  onChanged: (value) => print('新值: $value'),
)

// 迷你尺寸
AppInputNumber(
  value: 10,
  size: InputNumberSize.mini,
  onChanged: (value) => print('新值: $value'),
)
```

### 垂直布局

```dart
AppInputNumber(
  value: 10,
  size: InputNumberSize.large,
  controlsPosition: 'right', // 垂直布局
  onChanged: (value) => print('新值: $value'),
)
```

## 完整参数列表

```dart
AppInputNumber(
  value: 0,                           // 当前数值
  onChanged: (value) {},              // 数值变化回调
  disabled: false,                    // 是否禁用组件
  min: null,                          // 最小值限制
  max: null,                          // 最大值限制
  step: 1,                            // 每次增减的步长
  precision: 0,                       // 小数位数精度
  width: null,                        // 输入框宽度
  height: null,                       // 输入框高度（如果不指定，使用尺寸配置）
  placeholder: null,                  // 占位符文本
  showControls: true,                 // 是否显示控制按钮
  controlsPosition: 'both',           // 按钮位置：'right' | 'both'
  decoration: null,                   // 输入框样式
  textStyle: null,                    // 文本样式
  size: InputNumberSize.medium,       // 组件尺寸（新增）
)
```

## 使用场景

### 大尺寸 (Large)
- 重要的数值输入场景
- 需要突出显示的表单字段
- 触屏设备上需要更大点击区域的场景

### 中等尺寸 (Medium)
- 默认尺寸，适用于大多数常规场景
- 标准表单中的数值输入

### 小尺寸 (Small)
- 紧凑布局中的数值输入
- 表格中的内联编辑
- 工具栏中的参数调整

### 迷你尺寸 (Mini)
- 极度紧凑的空间
- 密集型数据表格
- 小型控制面板

## 注意事项

1. **高度优先级**：如果同时指定了 `height` 参数和 `size` 参数，`height` 参数优先级更高
2. **字体大小**：如果在 `textStyle` 中指定了 `fontSize`，将优先使用指定的字体大小
3. **兼容性**：新增的尺寸配置与现有的所有功能完全兼容
4. **响应式**：不同尺寸的组件会自动调整内边距、图标大小等细节，确保视觉效果协调

## 示例

查看 `example/input_number_size_example.dart` 文件获取完整的使用示例。
