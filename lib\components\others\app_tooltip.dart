import 'package:flutter/material.dart';

/// AppTooltip组件是对Flutter原生Tooltip的封装
///
/// 主要用于在用户悬停或长按时显示提示信息，帮助用户理解UI元素的功能
/// 比起原生Tooltip，它提供了更多自定义选项和默认配置：
/// - 默认20像素的垂直偏移
/// - 默认优先在目标上方显示
/// - 默认允许点击关闭提示
///
/// 使用示例:
/// ```dart
/// AppTooltip(
///   message: '点击添加新项目',
///   child: IconButton(
///     icon: Icon(Icons.add),
///     onPressed: () => addNewItem(),
///   ),
/// )
/// ```
class AppTooltip extends StatelessWidget {
  /// 提示文本内容
  final String message;

  /// 富文本提示内容，可以包含不同样式
  final InlineSpan? richMessage;

  /// 提示框高度
  final double? height;

  /// 提示框内部内容的内边距
  final EdgeInsetsGeometry? padding;

  /// 提示框外部的边距
  final EdgeInsetsGeometry? margin;

  /// 提示框与目标组件的垂直偏移量
  final double? verticalOffset;

  /// 是否优先在目标组件下方显示，true表示优先下方，false表示优先上方
  final bool? preferBelow;

  /// 是否从语义树中排除，用于辅助功能
  final bool? excludeFromSemantics;

  /// 提示框的自定义装饰，包括背景色、边框等
  final Decoration? decoration;

  /// 提示文本的样式
  final TextStyle? textStyle;

  /// 提示文本的对齐方式
  final TextAlign? textAlign;

  /// 鼠标悬停多久后显示提示，默认为系统设置
  final Duration? waitDuration;

  /// 提示框显示的持续时间，默认为系统设置
  final Duration? showDuration;

  /// 提示框消失的动画持续时间
  final Duration? exitDuration;

  /// 是否允许通过点击关闭提示框
  final bool enableTapToDismiss;

  /// 触发提示框显示的方式（鼠标悬停、长按等）
  final TooltipTriggerMode? triggerMode;

  /// 触发提示时是否提供触觉反馈
  final bool? enableFeedback;

  /// 提示框被触发时的回调函数
  final TooltipTriggeredCallback? onTriggered;

  /// 鼠标悬停在目标组件上时的光标样式
  final MouseCursor? mouseCursor;

  /// 需要添加提示的子组件
  final Widget child;

  const AppTooltip({
    super.key,
    required this.message,
    this.richMessage,
    this.height,
    this.padding,
    this.margin,
    this.verticalOffset = 20, // 默认垂直偏移20像素
    this.preferBelow = false, // 默认优先在上方显示
    this.excludeFromSemantics,
    this.decoration,
    this.textStyle,
    this.textAlign,
    this.waitDuration,
    this.showDuration,
    this.exitDuration,
    this.enableTapToDismiss = true, // 默认允许点击关闭提示
    this.triggerMode,
    this.enableFeedback,
    this.onTriggered,
    this.mouseCursor,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: message,
      richMessage: richMessage,
      height: height,
      padding: padding,
      margin: margin,
      verticalOffset: verticalOffset, // 设置提示框与目标组件之间的垂直距离
      preferBelow: preferBelow, // false时优先显示在目标组件上方，true时优先显示在下方
      excludeFromSemantics: excludeFromSemantics,
      decoration: decoration, // 自定义提示框的外观装饰
      textStyle: textStyle, // 提示文本的样式
      textAlign: textAlign, // 提示文本的对齐方式
      waitDuration: waitDuration, // 悬停多久后显示提示
      showDuration: showDuration, // 提示框显示的持续时间
      exitDuration: exitDuration, // 提示框消失的动画时长
      enableTapToDismiss: enableTapToDismiss, // 允许用户通过点击关闭提示
      triggerMode: triggerMode, // 触发方式：悬停、长按等
      enableFeedback: enableFeedback, // 是否启用触发时的触觉反馈
      onTriggered: onTriggered, // 提示框触发时的回调函数
      mouseCursor: mouseCursor, // 自定义鼠标光标样式
      child: child, // 包裹的子组件
    );
  }
}
