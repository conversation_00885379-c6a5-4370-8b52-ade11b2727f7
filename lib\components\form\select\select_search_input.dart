import 'package:flutter/material.dart';
import 'package:jyt_components_package/components/form/app_input.dart';
import 'package:jyt_components_package/theme/sizes.dart';

/// 选择器搜索输入框组件
class SelectSearchInput extends StatelessWidget {
  /// 输入控制器
  final TextEditingController? controller;

  /// 输入变化回调
  final void Function(String value)? onChanged;

  /// 提示文本
  final String hintText;

  /// 构造函数
  const SelectSearchInput({
    super.key,
    this.controller,
    this.onChanged,
    this.hintText = '搜索',
  });

  @override
  Widget build(BuildContext context) {
    return AppInput(
      controller: controller,
      hintText: hintText,
      prefixIcon: const Icon(Icons.search, size: AppIconSize.small),
      onChanged: onChanged,
    );
  }
}
