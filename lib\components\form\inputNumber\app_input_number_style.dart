import 'package:flutter/material.dart';

/// 数字输入框尺寸枚举
/// 定义了四种不同的组件尺寸选项
enum InputNumberSize {
  /// 大尺寸
  large,

  /// 中等尺寸
  medium,

  /// 小尺寸（默认）
  small,

  /// 迷你尺寸
  mini,
}

/// AppInputNumber 组件样式配置类
class AppInputNumberStyle {
  /// 默认组件高度
  static const double defaultHeight = 40.0;

  /// 默认字体大小
  static const double defaultFontSize = 14.0;

  /// 按钮宽度
  static const double buttonWidth = 32.0;

  /// 图标大小
  static const double iconSize = 16.0;

  /// 垂直按钮图标大小
  static const double verticalIconSize = 12.0;

  /// 垂直按钮宽度
  static const double verticalButtonWidth = 24.0;

  /// 不同尺寸的高度配置
  static const Map<InputNumberSize, double> _sizeHeights = {
    InputNumberSize.large: 48.0, // 大尺寸
    InputNumberSize.medium: 40.0, // 中等尺寸
    InputNumberSize.small: 32.0, // 小尺寸（默认）
    InputNumberSize.mini: 24.0, // 迷你尺寸
  };

  /// 不同尺寸的字体大小配置
  static const Map<InputNumberSize, double> _sizeFontSizes = {
    InputNumberSize.large: 16.0, // 大尺寸字体
    InputNumberSize.medium: 14.0, // 中等尺寸字体
    InputNumberSize.small: 12.0, // 小尺寸字体（默认）
    InputNumberSize.mini: 11.0, // 迷你尺寸字体
  };

  /// 不同尺寸的按钮宽度配置
  static const Map<InputNumberSize, double> _sizeButtonWidths = {
    InputNumberSize.large: 36.0, // 大尺寸按钮
    InputNumberSize.medium: 32.0, // 中等尺寸按钮
    InputNumberSize.small: 28.0, // 小尺寸按钮（默认）
    InputNumberSize.mini: 20.0, // 迷你尺寸按钮
  };

  /// 不同尺寸的图标大小配置
  static const Map<InputNumberSize, double> _sizeIconSizes = {
    InputNumberSize.large: 18.0, // 大尺寸图标
    InputNumberSize.medium: 16.0, // 中等尺寸图标
    InputNumberSize.small: 14.0, // 小尺寸图标（默认）
    InputNumberSize.mini: 12.0, // 迷你尺寸图标
  };

  /// 不同尺寸的垂直按钮宽度配置
  static const Map<InputNumberSize, double> _sizeVerticalButtonWidths = {
    InputNumberSize.large: 28.0, // 大尺寸垂直按钮
    InputNumberSize.medium: 24.0, // 中等尺寸垂直按钮
    InputNumberSize.small: 20.0, // 小尺寸垂直按钮（默认）
    InputNumberSize.mini: 16.0, // 迷你尺寸垂直按钮
  };

  /// 不同尺寸的水平内边距配置
  static const Map<InputNumberSize, double> _sizeHorizontalPaddings = {
    InputNumberSize.large: 12.0, // 大尺寸内边距
    InputNumberSize.medium: 8.0, // 中等尺寸内边距
    InputNumberSize.small: 6.0, // 小尺寸内边距（默认）
    InputNumberSize.mini: 4.0, // 迷你尺寸内边距
  };

  /// 根据尺寸获取组件高度
  static double getHeightBySize(InputNumberSize size) {
    return _sizeHeights[size] ?? defaultHeight;
  }

  /// 根据尺寸获取字体大小
  static double getFontSizeBySize(InputNumberSize size) {
    return _sizeFontSizes[size] ?? defaultFontSize;
  }

  /// 根据尺寸获取按钮宽度
  static double getButtonWidthBySize(InputNumberSize size) {
    return _sizeButtonWidths[size] ?? buttonWidth;
  }

  /// 根据尺寸获取图标大小
  static double getIconSizeBySize(InputNumberSize size) {
    return _sizeIconSizes[size] ?? iconSize;
  }

  /// 根据尺寸获取垂直按钮宽度
  static double getVerticalButtonWidthBySize(InputNumberSize size) {
    return _sizeVerticalButtonWidths[size] ?? verticalButtonWidth;
  }

  /// 根据尺寸获取水平内边距
  static double getHorizontalPaddingBySize(InputNumberSize size) {
    return _sizeHorizontalPaddings[size] ?? horizontalPadding;
  }

  /// 边框圆角半径
  static const double borderRadius = 4.0;

  /// 按钮圆角半径
  static const double buttonBorderRadius = 3.0;

  /// 水平内边距
  static const double horizontalPadding = 8.0;

  /// 基础间距
  static const double baseSpacing = 4.0;

  /// 获取边框颜色
  static Color getBorderColor({required bool disabled, required bool focused}) {
    if (disabled) return Colors.grey.shade200;
    if (focused) return Colors.blue;
    return Colors.grey.shade300;
  }

  /// 获取背景颜色
  static Color getBackgroundColor({required bool disabled}) {
    return disabled ? Colors.grey.shade50 : Colors.white;
  }

  /// 获取分隔线颜色
  static Color getSeparatorColor({required bool disabled}) {
    return disabled ? Colors.grey.shade200 : Colors.grey.shade300;
  }

  /// 获取图标颜色
  static Color getIconColor({required bool enabled}) {
    return enabled ? Colors.grey.shade700 : Colors.grey.shade400;
  }

  /// 获取提示文字颜色
  static Color getHintColor() {
    return Colors.grey.shade400;
  }

  /// 获取边框宽度
  static double getBorderWidth({required bool focused}) {
    return 1.0; // 所有状态下都使用1px边框
  }

  /// 获取外边距（用于补偿边框宽度变化）
  static double getMargin({required bool focused}) {
    return 0.0; // 边框宽度固定，不需要补偿
  }

  /// 计算垂直内边距
  static double calculateVerticalPadding({
    required double height,
    required double fontSize,
  }) {
    return ((height - fontSize - baseSpacing) / 2).clamp(0.0, double.infinity);
  }

  /// 获取文字样式
  static TextStyle getTextStyle({TextStyle? customStyle}) {
    return customStyle ??
        const TextStyle(fontSize: defaultFontSize, height: 1.0);
  }

  /// 获取提示文字样式
  static TextStyle getHintStyle({double? fontSize}) {
    return TextStyle(
      fontSize: fontSize ?? defaultFontSize,
      height: 1.0,
      color: getHintColor(),
    );
  }

  /// 获取左侧按钮圆角
  static BorderRadius getLeftButtonBorderRadius() {
    return const BorderRadius.only(
      topLeft: Radius.circular(buttonBorderRadius),
      bottomLeft: Radius.circular(buttonBorderRadius),
    );
  }

  /// 获取右侧按钮圆角
  static BorderRadius getRightButtonBorderRadius() {
    return const BorderRadius.only(
      topRight: Radius.circular(buttonBorderRadius),
      bottomRight: Radius.circular(buttonBorderRadius),
    );
  }

  /// 获取垂直按钮上方圆角
  static BorderRadius getVerticalTopButtonBorderRadius() {
    return const BorderRadius.only(
      topRight: Radius.circular(buttonBorderRadius),
    );
  }

  /// 获取垂直按钮下方圆角
  static BorderRadius getVerticalBottomButtonBorderRadius() {
    return const BorderRadius.only(
      bottomRight: Radius.circular(buttonBorderRadius),
    );
  }

  /// 获取容器圆角
  static BorderRadius getContainerBorderRadius() {
    return BorderRadius.circular(borderRadius);
  }

  /// 获取垂直控制按钮容器圆角
  static BorderRadius getVerticalControlsBorderRadius() {
    return const BorderRadius.only(
      topRight: Radius.circular(borderRadius),
      bottomRight: Radius.circular(borderRadius),
    );
  }
}
