import 'package:flutter/material.dart';
import 'select_model.dart';

/// 选择器控制器，用于管理选择器的状态和行为
class SelectController<T> extends ChangeNotifier {
  /// 单选模式的当前选中值
  T? _value;

  /// 多选模式的选中值列表
  List<T> _multiValues = [];

  /// 选项列表
  List<SelectOption<T>> _options = [];

  /// 过滤后的选项列表
  List<SelectOption<T>> _filteredOptions = [];

  /// 搜索关键词
  String _searchText = '';

  /// 过滤模式
  SelectFilterMode _filterMode = SelectFilterMode.contains;

  /// 自定义过滤函数
  bool Function(SelectOption<T> option, String searchText)? _customFilterFn;

  /// 是否处于多选模式
  final bool _multiple;

  /// 是否展开下拉面板
  bool _isExpanded = false;

  /// 是否正在搜索
  bool _isSearching = false;

  /// 是否正在加载远程数据
  bool _isLoadingRemoteData = false;

  /// 当前选中值（单选模式）
  T? get value => _value;

  /// 当前选中值列表（多选模式）
  List<T> get multiValues => List.unmodifiable(_multiValues);

  /// 选项列表
  List<SelectOption<T>> get options => List.unmodifiable(_options);

  /// 过滤后的选项列表
  List<SelectOption<T>> get filteredOptions => List.unmodifiable(_filteredOptions);

  /// 搜索关键词
  String get searchText => _searchText;

  /// 是否处于多选模式
  bool get isMultiple => _multiple;

  /// 是否展开下拉面板
  bool get isExpanded => _isExpanded;

  /// 是否正在搜索
  bool get isSearching => _isSearching;

  /// 是否正在加载远程数据
  bool get isLoadingRemoteData => _isLoadingRemoteData;

  /// 是否有选中值
  bool get hasValue => _multiple ? _multiValues.isNotEmpty : _value != null;

  /// 选中项信息（单选模式）
  SelectOption<T>? get selectedOption {
    if (!hasValue || _multiple) return null;
    return SelectOption.findByValue(_options, _value);
  }

  /// 选中项列表（多选模式）
  List<SelectOption<T>> get selectedOptions {
    if (!hasValue || !_multiple) return [];
    return SelectOption.findMultipleByValues(_options, _multiValues);
  }

  /// 创建选择器控制器
  ///
  /// [value] 初始选中值（单选模式）
  /// [multiValues] 初始选中值列表（多选模式）
  /// [multiple] 是否多选模式
  /// [options] 选项列表
  SelectController({
    T? value,
    List<T>? multiValues,
    bool multiple = false,
    List<SelectOption<T>>? options,
  }) : _multiple = multiple {
    _value = value;
    if (multiValues != null) {
      _multiValues = List<T>.from(multiValues);
    }
    if (options != null) {
      setOptions(options);
    }
  }

  /// 设置选项列表
  void setOptions(List<SelectOption<T>> options) {
    _options = List<SelectOption<T>>.from(options);
    _applyFilter();
  }

  /// 展开下拉面板
  void expand() {
    if (!_isExpanded) {
      _isExpanded = true;
      notifyListeners();
    }
  }

  /// 收起下拉面板
  void collapse() {
    if (_isExpanded) {
      _isExpanded = false;
      notifyListeners();
    }
  }

  /// 切换下拉面板展开状态
  void toggle() {
    _isExpanded = !_isExpanded;
    notifyListeners();
  }

  /// 设置单选值
  void setValue(T? value) {
    if (_multiple) {
      throw UnsupportedError('在多选模式下，请使用 setMultiValues 方法设置值');
    }
    if (_value != value) {
      _value = value;
      notifyListeners();
    }
  }

  /// 设置多选值
  void setMultiValues(List<T> values) {
    if (!_multiple) {
      throw UnsupportedError('在单选模式下，请使用 setValue 方法设置值');
    }

    final newValues = List<T>.from(values);
    if (!_areListsEqual(_multiValues, newValues)) {
      _multiValues = newValues;
      notifyListeners();
    }
  }

  /// 添加值到多选列表
  void addValue(T value) {
    if (!_multiple) {
      throw UnsupportedError('在单选模式下，请使用 setValue 方法设置值');
    }
    if (!_multiValues.contains(value)) {
      _multiValues.add(value);
      notifyListeners();
    }
  }

  /// 从多选列表移除值
  void removeValue(T value) {
    if (!_multiple) {
      throw UnsupportedError('在单选模式下，请使用 setValue 方法设置值');
    }
    if (_multiValues.contains(value)) {
      _multiValues.remove(value);
      notifyListeners();
    }
  }

  /// 切换值在多选列表中的状态
  void toggleValue(T value) {
    if (!_multiple) {
      setValue(value);
      return;
    }

    final newValues = List<T>.from(_multiValues);
    if (newValues.contains(value)) {
      newValues.remove(value);
    } else {
      newValues.add(value);
    }

    _multiValues = newValues;
    notifyListeners();
  }

  /// 清空选中值
  void clear() {
    bool changed = false;

    if (_multiple) {
      if (_multiValues.isNotEmpty) {
        _multiValues = [];
        changed = true;
      }
    } else {
      if (_value != null) {
        _value = null;
        changed = true;
      }
    }

    if (changed) {
      notifyListeners();
    }
  }

  /// 设置搜索关键词和过滤模式
  void _updateSearch(
    String? searchText,
    SelectFilterMode? filterMode,
    bool Function(SelectOption<T>, String)? customFilterFn,
  ) {
    bool changed = false;

    if (searchText != null && _searchText != searchText) {
      _searchText = searchText;
      changed = true;
    }

    if (filterMode != null && _filterMode != filterMode) {
      _filterMode = filterMode;
      changed = true;
    }

    if (customFilterFn != null && _customFilterFn != customFilterFn) {
      _customFilterFn = customFilterFn;
      changed = true;
    }

    if (changed) {
      _applyFilter();
      notifyListeners();
    }
  }

  /// 设置搜索关键词
  void setSearchText(String searchText) {
    _updateSearch(searchText, null, null);
  }

  /// 设置过滤模式
  void setFilterMode(SelectFilterMode filterMode) {
    _updateSearch(null, filterMode, null);
  }

  /// 设置自定义过滤函数
  void setCustomFilterFn(bool Function(SelectOption<T> option, String searchText) filterFn) {
    _updateSearch(null, null, filterFn);
  }

  /// 执行远程搜索
  Future<void> remoteSearch(
    Future<List<SelectOption<T>>> Function(String query) remoteMethod,
  ) async {
    _isLoadingRemoteData = true;
    notifyListeners();

    try {
      final results = await remoteMethod(_searchText);
      _filteredOptions = results;
    } catch (e) {
      // 处理错误
      _filteredOptions = [];
    } finally {
      _isLoadingRemoteData = false;
      notifyListeners();
    }
  }

  /// 应用过滤条件
  void _applyFilter() {
    _isSearching = _searchText.isNotEmpty;

    if (_filterMode == SelectFilterMode.remote) {
      // 远程搜索模式下不在本地过滤
      return;
    }

    if (_searchText.isEmpty) {
      _filteredOptions = List<SelectOption<T>>.from(_options);
      return;
    }

    final search = _searchText.toLowerCase();
    _filteredOptions =
        _options.where((option) {
          if (_filterMode == SelectFilterMode.custom && _customFilterFn != null) {
            return _customFilterFn!(option, _searchText);
          }

          final label = option.label.toLowerCase();

          return _filterMode == SelectFilterMode.startsWith
              ? label.startsWith(search)
              : label.contains(search);
        }).toList();
  }

  /// 判断两个列表是否相等
  bool _areListsEqual(List<T> list1, List<T> list2) {
    if (list1.length != list2.length) return false;

    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) return false;
    }

    return true;
  }

  @override
  void dispose() {
    _options = [];
    _filteredOptions = [];
    _multiValues = [];
    _value = null;
    super.dispose();
  }
}
