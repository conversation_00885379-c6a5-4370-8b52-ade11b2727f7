import 'package:flutter/material.dart';
import 'package:jyt_components_package/theme/colors.dart';
import 'package:jyt_components_package/theme/sizes.dart';

/// 按钮主题
class AppButtonTheme {
  /// 亮色主题按钮样式
  static ElevatedButtonThemeData elevatedButtonTheme() {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textWhite,
        disabledBackgroundColor: AppColors.primaryDisabled,
        disabledForegroundColor: AppColors.white,
        padding: const EdgeInsets.only(
          left: 16.0,
          top: 10.0,
          right: 16.0,
          bottom: 12.0,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        elevation: 2,
        iconSize: AppIconSize.medium,
      ),
    );
  }

  /// 轮廓按钮样式
  static OutlinedButtonThemeData outlinedButtonTheme() {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.primary,
        disabledForegroundColor: AppColors.textDisabled,
        side: BorderSide(color: AppColors.primary),
        padding: const EdgeInsets.only(
          left: 16.0,
          top: 10.0,
          right: 16.0,
          bottom: 12.0,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        iconSize: AppIconSize.medium,
      ),
    );
  }

  /// 文本按钮样式
  static TextButtonThemeData textButtonTheme() {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColors.primary,
        disabledForegroundColor: AppColors.textDisabled,
        padding: const EdgeInsets.only(
          left: 16.0,
          top: 10.0,
          right: 16.0,
          bottom: 12.0,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        iconSize: AppIconSize.medium,
      ),
    );
  }

  /// 浮动按钮样式
  static FloatingActionButtonThemeData floatingActionButtonTheme() {
    return FloatingActionButtonThemeData(
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.textWhite,
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    );
  }

  /// 图标按钮样式
  static IconButtonThemeData iconButtonTheme() {
    return IconButtonThemeData(
      style: IconButton.styleFrom(
        foregroundColor: AppColors.icon300,
        iconSize: AppIconSize.medium,
      ),
    );
  }

  /// 暗黑主题图标按钮样式
  static IconButtonThemeData darkIconButtonTheme() {
    return IconButtonThemeData(
      style: IconButton.styleFrom(
        foregroundColor: AppColors.iconDark300,
        iconSize: AppIconSize.medium,
      ),
    );
  }
}
