import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:jyt_components_package/theme/sizes.dart';

/// 图片缓存工具类
/// 提供自定义缓存管理功能，包括缓存时间设置、缓存清理等
class ImageCacheUtil {
  /// 默认缓存时间 (7天)
  static const Duration defaultStalePeriod = Duration(days: 7);

  /// 默认缓存对象最大数量
  static const int defaultMaxObjects = 200;

  /// 默认缓存键
  static const String defaultCacheKey = 'octasync_image_cache';

  /// 预定义的缓存管理器
  static final Map<String, CacheManager> _cacheManagers = {
    defaultCacheKey: _createManager(
      defaultCacheKey,
      defaultStalePeriod,
      defaultMaxObjects,
    ),
    'avatar_cache': _createManager(
      'avatar_cache',
      const Duration(days: 30),
      500,
    ),
  };

  /// 获取默认缓存管理器
  static CacheManager get defaultCacheManager =>
      _cacheManagers[defaultCacheKey]!;

  /// 获取头像缓存管理器
  static CacheManager get avatarCacheManager => _cacheManagers['avatar_cache']!;

  /// 创建自定义缓存管理器
  /// [cacheKey] - 缓存键，用于区分不同的缓存
  /// [stalePeriod] - 缓存过期时间
  /// [maxObjects] - 最大缓存对象数量
  static CacheManager createCacheManager({
    required String cacheKey,
    Duration? stalePeriod,
    int? maxObjects,
  }) {
    if (_cacheManagers.containsKey(cacheKey)) {
      return _cacheManagers[cacheKey]!;
    }

    final manager = _createManager(
      cacheKey,
      stalePeriod ?? defaultStalePeriod,
      maxObjects ?? defaultMaxObjects,
    );

    _cacheManagers[cacheKey] = manager;
    return manager;
  }

  /// 内部创建缓存管理器的方法
  static CacheManager _createManager(
    String key,
    Duration stalePeriod,
    int maxObjects,
  ) {
    return CacheManager(
      Config(key, stalePeriod: stalePeriod, maxNrOfCacheObjects: maxObjects),
    );
  }

  /// 清除指定URL的缓存
  static Future<void> removeFileFromCache(
    String url, {
    CacheManager? cacheManager,
  }) async {
    await (cacheManager ?? defaultCacheManager).removeFile(url);
  }

  /// 清空所有缓存
  static Future<void> clear() async {
    for (final manager in _cacheManagers.values) {
      await manager.emptyCache();
    }
  }

  /// 获取缓存文件路径
  static Future<String?> getCachedFilePath(
    String url, {
    CacheManager? cacheManager,
  }) async {
    final fileInfo = await (cacheManager ?? defaultCacheManager)
        .getFileFromCache(url);
    return fileInfo?.file.path;
  }

  /// 预缓存图片
  static Future<void> precacheImage(
    String url, {
    CacheManager? cacheManager,
  }) async {
    await (cacheManager ?? defaultCacheManager).getSingleFile(url);
  }

  /// 创建带缓存的网络图片组件(默认带4px圆角)
  static Widget cachedNetworkImage({
    String? imageUrl,
    double? width,
    double? height,
    double? radius = AppRadiusSize.radius4,
    BoxFit fit = BoxFit.cover,
    Widget Function(BuildContext, String, dynamic)? errorWidget,
    Widget? placeholder,
    CacheManager? cacheManager,
    bool useOldImageOnUrlChange = false,
    bool noAnimation = false, // 禁用动画
  }) {
    Widget defaultImage = Image.asset(
      'assets/images/default_image.png',
      width: width,
      height: height,
      fit: BoxFit.cover,
    );

    // 如果imageUrl为空，直接显示默认头像
    if (imageUrl == null || imageUrl.isEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(radius ?? 0),
        child: defaultImage,
      );
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(radius ?? 0),
      child: CachedNetworkImage(
        imageUrl: imageUrl,
        width: width,
        height: height,
        fit: fit,
        cacheManager: cacheManager ?? defaultCacheManager,
        placeholder: placeholder != null
            ? (context, url) => placeholder
            : (context, url) => defaultImage,
        errorWidget: errorWidget ?? (context, url, error) => defaultImage,
        useOldImageOnUrlChange: useOldImageOnUrlChange,
        // 禁用所有动画
        fadeOutDuration: noAnimation
            ? Duration.zero
            : const Duration(milliseconds: 300),
        fadeInDuration: noAnimation
            ? Duration.zero
            : const Duration(milliseconds: 300),
        placeholderFadeInDuration: noAnimation
            ? Duration.zero
            : const Duration(milliseconds: 500),
      ),
    );
  }

  /// 创建带缓存的圆形头像组件
  static Widget cachedAvatarImage({
    String? imageUrl,
    double size = 24,
    double? radius, // 默认圆角
    Widget Function(BuildContext, String, dynamic)? errorWidget,
    Widget? placeholder,
    Duration? placeholderFadeInDuration,
  }) {
    Widget defaultAvatar = ClipRRect(
      borderRadius: BorderRadius.circular(radius ?? size / 2),
      child: Image.asset(
        'assets/images/default_avatar.png',
        width: size,
        height: size,
        fit: BoxFit.cover,
      ),
    );

    // 如果imageUrl为空，直接显示默认头像
    if (imageUrl == null || imageUrl.isEmpty) return defaultAvatar;

    return cachedNetworkImage(
      imageUrl: imageUrl,
      width: size,
      height: size,
      cacheManager: avatarCacheManager,
      errorWidget: errorWidget ?? (context, url, error) => defaultAvatar,
      radius: radius ?? size / 2,
    );
  }
}
