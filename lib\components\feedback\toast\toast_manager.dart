import 'dart:async';
import 'package:flutter/material.dart';
import 'package:jyt_components_package/components/feedback/toast/app_toast.dart';

/// Toast位置
enum ToastPosition { top, bottom }

///  Toast 的管理器，负责 Toast 的显示、隐藏和动画效果。
class ToastManager {
  static final ToastManager instance = ToastManager._();
  ToastManager._();

  // 成员变量
  OverlayEntry? _overlayEntry;
  Timer? _timer;
  AnimationController? _animationController;
  Animation<double>? _fadeAnimation;

  // 全局变量
  static late GlobalKey<NavigatorState> _navigatorKey;
  static const Duration _defaultDuration = Duration(seconds: 3);
  static const Duration _animationDuration = Duration(milliseconds: 250);

  /// 初始化全局context
  static void init(GlobalKey<NavigatorState> navigatorKey) {
    _navigatorKey = navigatorKey;
  }

  /// 获取OverlayState
  OverlayState? get _overlay => _navigatorKey.currentState?.overlay;

  /// 抛出的错误消息转换为字符串
  static String _formatMessage(dynamic message) {
    if (message == null) return '';
    if (message is Exception) return message.toString().replaceAll('Exception: ', '');
    if (message is Error) return message.toString();
    return message.toString();
  }

  /// 显示Toast
  void show({
    required dynamic message,
    ToastType type = ToastType.info,
    Duration duration = _defaultDuration,
    ToastPosition position = ToastPosition.top,
  }) {
    final formattedMessage = _formatMessage(message);
    final overlay = _overlay;
    if (overlay == null) {
      debugPrint('Toast错误: 无法获取Overlay');
      return;
    }

    // 使用addPostFrameCallback确保在构建完成后显示Toast
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 如果已经有 Toast 在显示，先移除
      _hide();

      try {
        // 创建动画控制器
        _animationController = AnimationController(vsync: overlay, duration: _animationDuration);
        _fadeAnimation = CurvedAnimation(parent: _animationController!, curve: Curves.easeInOut);

        // 创建新的 OverlayEntry
        _overlayEntry = OverlayEntry(
          builder: (context) => _buildToastWidget(context, formattedMessage, type, position),
        );

        // 将 OverlayEntry 添加到 Overlay并播放动画
        overlay.insert(_overlayEntry!);
        _animationController!.forward();

        // 设置定时器，自动隐藏
        _timer = Timer(duration, () {
          _animationController?.reverse().then((_) => _hide());
        });
      } catch (e) {
        debugPrint('Toast显示错误: $e');
        _hide();
      }
    });
  }

  /// 构建Toast小部件
  Widget _buildToastWidget(
    BuildContext context,
    String message,
    ToastType type,
    ToastPosition position,
  ) {
    final mediaQuery = MediaQuery.of(context);
    final topPadding = mediaQuery.padding.top;
    final bottomPadding = mediaQuery.padding.bottom;

    return Positioned(
      top: position == ToastPosition.top ? topPadding + 20 : null,
      bottom: position == ToastPosition.bottom ? bottomPadding + 20 : null,
      left: 0,
      right: 0,
      child: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation!,
          child: Center(
            child: Material(
              color: Colors.transparent,
              child: AppToast(message: message, type: type),
            ),
          ),
        ),
      ),
    );
  }

  /// 隐藏Toast
  void _hide() {
    _timer?.cancel();
    _timer = null;
    _animationController?.dispose();
    _animationController = null;
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  /// 显示不同类型的Toast的静态方法
  static void success(
    String message, {
    Duration? duration,
    ToastPosition position = ToastPosition.top,
  }) {
    instance.show(
      message: message,
      type: ToastType.success,
      duration: duration ?? _defaultDuration,
      position: position,
    );
  }

  /// 显示错误Toast
  static void error(
    dynamic message, {
    Duration? duration,
    ToastPosition position = ToastPosition.top,
  }) {
    instance.show(
      message: message,
      type: ToastType.error,
      duration: duration ?? _defaultDuration,
      position: position,
    );
  }

  /// 显示警告Toast
  static void warning(
    String message, {
    Duration? duration,
    ToastPosition position = ToastPosition.top,
  }) {
    instance.show(
      message: message,
      type: ToastType.warning,
      duration: duration ?? _defaultDuration,
      position: position,
    );
  }

  /// 显示信息Toast
  static void info(
    String message, {
    Duration? duration,
    ToastPosition position = ToastPosition.top,
  }) {
    instance.show(
      message: message,
      type: ToastType.info,
      duration: duration ?? _defaultDuration,
      position: position,
    );
  }
}
