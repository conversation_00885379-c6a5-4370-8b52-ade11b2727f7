import 'package:flutter/material.dart';
import 'package:jyt_components_package/components/form/select/select_model.dart';
import 'package:jyt_components_package/theme/colors.dart';
import 'package:jyt_components_package/theme/sizes.dart';

/// 选择器多选标签组件
class SelectTag<T> extends StatelessWidget {
  /// 选项数据
  final SelectOption<T> option;

  /// 移除标签回调
  final VoidCallback onRemove;

  /// 是否禁用
  final bool disabled;

  /// 字体大小
  final double fontSize;

  /// 标签构建器
  final Widget Function(
    BuildContext context,
    SelectOption<T> option,
    VoidCallback onRemove,
  )?
  tagBuilder;

  /// 构造函数
  const SelectTag({
    super.key,
    required this.option,
    required this.onRemove,
    this.disabled = false,
    this.fontSize = 13,
    this.tagBuilder,
  });

  @override
  Widget build(BuildContext context) {
    if (tagBuilder != null) {
      return tagBuilder!(context, option, onRemove);
    }

    return _buildDefaultTag(context);
  }

  /// 构建默认标签样式
  Widget _buildDefaultTag(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.light
            ? AppColors.background200
            : AppColors.backgroundDark200,
        borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            option.label,
            style: TextStyle(
              fontSize: fontSize,
              color: option.disabled || disabled
                  ? Theme.of(context).disabledColor
                  : context.textPrimary,
            ),
          ),
          if (!disabled && !option.disabled) ...[
            const SizedBox(width: 4),
            GestureDetector(
              onTap: onRemove,
              child: Icon(Icons.close, size: 12, color: context.textSecondary),
            ),
          ],
        ],
      ),
    );
  }
}

/// 选择器多选标签组
class SelectTagList<T> extends StatelessWidget {
  /// 选中的选项列表
  final List<SelectOption<T>> selectedOptions;

  /// 移除标签回调
  final void Function(T value) onRemoveTag;

  /// 是否折叠标签
  final bool collapseTags;

  /// 最大显示标签数量
  final int maxCollapseTagCount;

  /// 是否禁用
  final bool disabled;

  /// 字体大小
  final double fontSize;

  /// 标签构建器
  final Widget Function(
    BuildContext context,
    SelectOption<T> option,
    VoidCallback onRemove,
  )?
  tagBuilder;

  /// 构造函数
  const SelectTagList({
    super.key,
    required this.selectedOptions,
    required this.onRemoveTag,
    this.collapseTags = false,
    this.maxCollapseTagCount = 1,
    this.disabled = false,
    this.fontSize = 13,
    this.tagBuilder,
  });

  @override
  Widget build(BuildContext context) {
    if (selectedOptions.isEmpty) {
      return const SizedBox();
    }

    // 是否需要折叠标签
    final bool shouldCollapse =
        collapseTags && selectedOptions.length > maxCollapseTagCount;

    // 显示的标签数量
    final int visibleTagCount = shouldCollapse
        ? maxCollapseTagCount
        : selectedOptions.length;

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          // 显示的标签
          ...List.generate(visibleTagCount, (index) {
            final option = selectedOptions[index];
            return Padding(
              padding: const EdgeInsets.only(right: 4),
              child: SelectTag<T>(
                option: option,
                onRemove: () => onRemoveTag(option.value),
                disabled: disabled,
                fontSize: fontSize,
                tagBuilder: tagBuilder,
              ),
            );
          }),

          // 折叠剩余标签的数量提示
          if (shouldCollapse)
            Text(
              '+${selectedOptions.length - visibleTagCount}',
              style: TextStyle(
                fontSize: fontSize - 1,
                color: context.textSecondary,
              ),
            ),
        ],
      ),
    );
  }
}
