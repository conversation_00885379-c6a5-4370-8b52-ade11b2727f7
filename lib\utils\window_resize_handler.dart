import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:window_manager/window_manager.dart';

class WindowResizeHandler extends StatefulWidget {
  final Widget child;
  final double borderWidth;

  const WindowResizeHandler({super.key, required this.child, this.borderWidth = 6.0});

  @override
  State<WindowResizeHandler> createState() => _WindowResizeHandlerState();
}

class _WindowResizeHandlerState extends State<WindowResizeHandler> {
  bool _isResizing = false;

  @override
  void initState() {
    super.initState();
    // 注册服务绑定键盘处理器
    ServicesBinding.instance.keyboard.addHandler(_handleKeyboardEvent);
  }

  @override
  void dispose() {
    // 移除键盘处理器
    ServicesBinding.instance.keyboard.removeHandler(_handleKeyboardEvent);
    super.dispose();
  }

  // 处理键盘事件
  bool _handleKeyboardEvent(KeyEvent event) {
    // 如果正在调整大小，可能需要特殊处理一些按键状态
    if (_isResizing) {
      if (event is KeyDownEvent &&
          HardwareKeyboard.instance.isPhysicalKeyPressed(event.physicalKey)) {
        return true; // 表示已处理事件
      }
    }
    return false; // 继续正常的事件流
  }

  // 开始调整大小
  void _startResizing(ResizeEdge edge) {
    setState(() {
      _isResizing = true;
    });
    windowManager.startResizing(edge);
  }

  // 结束调整大小
  void _endResizing() {
    setState(() {
      _isResizing = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerUp: (_) => _endResizing(),
      child: Stack(
        children: [
          widget.child,
          // 左边缘
          Positioned(
            left: 0,
            top: widget.borderWidth,
            bottom: widget.borderWidth,
            width: widget.borderWidth,
            child: MouseRegion(
              cursor: SystemMouseCursors.resizeLeftRight,
              child: GestureDetector(
                onPanStart: (_) => _startResizing(ResizeEdge.left),
                behavior: HitTestBehavior.translucent,
                child: Container(color: Colors.transparent),
              ),
            ),
          ),
          // 右边缘
          Positioned(
            right: 0,
            top: widget.borderWidth,
            bottom: widget.borderWidth,
            width: widget.borderWidth,
            child: MouseRegion(
              cursor: SystemMouseCursors.resizeLeftRight,
              child: GestureDetector(
                onPanStart: (_) => _startResizing(ResizeEdge.right),
                behavior: HitTestBehavior.translucent,
                child: Container(color: Colors.transparent),
              ),
            ),
          ),
          // 顶边缘
          Positioned(
            top: 0,
            left: widget.borderWidth,
            right: widget.borderWidth,
            height: widget.borderWidth,
            child: MouseRegion(
              cursor: SystemMouseCursors.resizeUpDown,
              child: GestureDetector(
                onPanStart: (_) => _startResizing(ResizeEdge.top),
                behavior: HitTestBehavior.translucent,
                child: Container(color: Colors.transparent),
              ),
            ),
          ),
          // 底边缘
          Positioned(
            bottom: 0,
            left: widget.borderWidth,
            right: widget.borderWidth,
            height: widget.borderWidth,
            child: MouseRegion(
              cursor: SystemMouseCursors.resizeUpDown,
              child: GestureDetector(
                onPanStart: (_) => _startResizing(ResizeEdge.bottom),
                behavior: HitTestBehavior.translucent,
                child: Container(color: Colors.transparent),
              ),
            ),
          ),
          // 左上角
          Positioned(
            left: 0,
            top: 0,
            width: widget.borderWidth,
            height: widget.borderWidth,
            child: MouseRegion(
              cursor: SystemMouseCursors.resizeUpLeftDownRight,
              child: GestureDetector(
                onPanStart: (_) => _startResizing(ResizeEdge.topLeft),
                behavior: HitTestBehavior.translucent,
                child: Container(color: Colors.transparent),
              ),
            ),
          ),
          // 右上角
          Positioned(
            right: 0,
            top: 0,
            width: widget.borderWidth,
            height: widget.borderWidth,
            child: MouseRegion(
              cursor: SystemMouseCursors.resizeUpRightDownLeft,
              child: GestureDetector(
                onPanStart: (_) => _startResizing(ResizeEdge.topRight),
                behavior: HitTestBehavior.translucent,
                child: Container(color: Colors.transparent),
              ),
            ),
          ),
          // 左下角
          Positioned(
            left: 0,
            bottom: 0,
            width: widget.borderWidth,
            height: widget.borderWidth,
            child: MouseRegion(
              cursor: SystemMouseCursors.resizeUpRightDownLeft,
              child: GestureDetector(
                onPanStart: (_) => _startResizing(ResizeEdge.bottomLeft),
                behavior: HitTestBehavior.translucent,
                child: Container(color: Colors.transparent),
              ),
            ),
          ),
          // 右下角
          Positioned(
            right: 0,
            bottom: 0,
            width: widget.borderWidth,
            height: widget.borderWidth,
            child: MouseRegion(
              cursor: SystemMouseCursors.resizeUpLeftDownRight,
              child: GestureDetector(
                onPanStart: (_) => _startResizing(ResizeEdge.bottomRight),
                behavior: HitTestBehavior.translucent,
                child: Container(color: Colors.transparent),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
