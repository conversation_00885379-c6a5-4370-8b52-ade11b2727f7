import 'package:flutter/material.dart';
import 'package:jyt_components_package/theme/colors.dart';
import 'select_model.dart';
import 'select_option.dart';
import 'select_search_input.dart';

/// 下拉选择器下拉列表组件
class SelectDropdown<T> extends StatelessWidget {
  /// 选项列表
  final List<SelectOption<T>> options;

  /// 选项选中状态检查函数
  final bool Function(SelectOption<T> option) isOptionSelected;

  /// 选项点击回调
  final void Function(SelectOption<T> option) onOptionSelected;

  /// 搜索输入变化回调
  final void Function(String value)? onSearch;

  /// 是否过滤搜索
  final bool filterable;

  /// 下拉宽度
  final double dropdownWidth;

  /// 选项高度
  final double optionHeight;

  /// 下拉最大高度
  final double? popupMaxHeight;

  /// 是否启用虚拟滚动
  final bool virtualScroll;

  /// 自定义选项构建器
  final Widget Function(BuildContext context, SelectOption<T> option, bool selected)? optionBuilder;

  /// 是否多选模式
  final bool multiple;

  /// 是否正在加载远程数据
  final bool isLoadingRemoteData;

  /// 无匹配数据提示文本
  final String noMatchText;

  /// 搜索框控制器
  final TextEditingController? searchController;

  /// 构造函数
  const SelectDropdown({
    super.key,
    required this.options,
    required this.isOptionSelected,
    required this.onOptionSelected,
    required this.dropdownWidth,
    required this.optionHeight,
    this.onSearch,
    this.filterable = false,
    this.popupMaxHeight,
    this.virtualScroll = false,
    this.optionBuilder,
    this.multiple = false,
    this.isLoadingRemoteData = false,
    this.noMatchText = '无匹配数据',
    this.searchController,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: dropdownWidth,
      constraints: BoxConstraints(minWidth: dropdownWidth, maxWidth: dropdownWidth),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 搜索框（如果启用了搜索功能）
          if (filterable) ...[
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: SelectSearchInput(controller: searchController, onChanged: onSearch),
            ),
          ],

          // 加载指示器（远程搜索时）
          if (isLoadingRemoteData) ...[
            Container(
              width: dropdownWidth,
              padding: const EdgeInsets.all(16),
              alignment: Alignment.center,
              child: const CircularProgressIndicator(strokeWidth: 2),
            ),
          ]
          // 空数据提示
          else if (options.isEmpty) ...[
            Container(
              width: dropdownWidth,
              padding: const EdgeInsets.all(16),
              alignment: Alignment.center,
              child: Text(
                noMatchText,
                style: TextStyle(color: AppColors.textSecondary, fontSize: 14),
              ),
            ),
          ]
          // 选项列表
          else ...[
            virtualScroll ? _buildVirtualOptionsList() : _buildNormalOptionsList(),
          ],
        ],
      ),
    );
  }

  /// 构建普通选项列表
  Widget _buildNormalOptionsList() {
    return SizedBox(
      width: dropdownWidth,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: List.generate(
          options.length,
          (index) => SizedBox(
            width: dropdownWidth,
            child: SelectOptionItem<T>(
              option: options[index],
              selected: isOptionSelected(options[index]),
              multiple: multiple,
              onTap: () => onOptionSelected(options[index]),
              itemBuilder: optionBuilder,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建虚拟滚动选项列表（大数据量）
  Widget _buildVirtualOptionsList() {
    return SizedBox(
      width: dropdownWidth,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxHeight: popupMaxHeight ?? 300,
          minWidth: dropdownWidth,
          maxWidth: dropdownWidth,
        ),
        child: ListView.builder(
          itemCount: options.length,
          itemExtent: optionHeight,
          itemBuilder:
              (context, index) => SizedBox(
                width: dropdownWidth,
                child: SelectOptionItem<T>(
                  option: options[index],
                  selected: isOptionSelected(options[index]),
                  multiple: multiple,
                  onTap: () => onOptionSelected(options[index]),
                  itemBuilder: optionBuilder,
                ),
              ),
        ),
      ),
    );
  }
}
