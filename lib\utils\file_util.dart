import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';

/// 文件工具类，提供文件选择和处理相关功能
class FileUtil {
  /// 选择单个文件并返回文件内容
  ///
  /// [type] - 文件类型过滤器，默认为任何类型
  /// [allowedExtensions] - 允许的文件扩展名列表，当[type]为[FileType.custom]时使用
  ///
  /// 如果用户取消选择则返回null
  static Future<PlatformFile?> pickSingleFile({
    FileType type = FileType.any,
    List<String>? allowedExtensions,
  }) async {
    try {
      final FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: type,
        allowedExtensions: type == FileType.custom ? allowedExtensions : null,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        // Web平台直接返回bytes
        if (kIsWeb) {
          return file;
        }
        // 桌面端从文件路径读取bytes
        if (file.path != null) {
          final bytes = await File(file.path!).readAsBytes();
          return PlatformFile(name: file.name, path: file.path, size: file.size, bytes: bytes);
        }
      }

      return null;
    } catch (e) {
      print('选择文件错误: $e');
      return null;
    }
  }

  /// 选择多个文件并返回文件内容列表
  ///
  /// [type] - 文件类型过滤器，默认为任何类型
  /// [allowedExtensions] - 允许的文件扩展名列表，当[type]为[FileType.custom]时使用
  ///
  /// 如果用户取消选择则返回空列表
  static Future<List<PlatformFile>> pickMultipleFiles({
    FileType type = FileType.any,
    List<String>? allowedExtensions,
  }) async {
    try {
      final FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: type,
        allowedExtensions: type == FileType.custom ? allowedExtensions : null,
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final files = result.files;
        if (kIsWeb) {
          // Web平台直接返回带bytes的文件
          return files.where((file) => file.bytes != null).toList();
        }

        // 桌面端读取文件bytes
        final List<PlatformFile> processedFiles = [];
        for (final file in files) {
          if (file.path != null) {
            try {
              final bytes = await File(file.path!).readAsBytes();
              processedFiles.add(
                PlatformFile(name: file.name, path: file.path, size: file.size, bytes: bytes),
              );
            } catch (e) {
              print('读取文件错误: ${file.name} - $e');
            }
          }
        }
        return processedFiles;
      }
      return [];
    } catch (e) {
      print('多文件选择错误: $e');
      return [];
    }
  }
}
