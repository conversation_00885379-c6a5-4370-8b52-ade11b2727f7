import 'package:flutter/services.dart';

/// 自定义文本输入格式化器
/// 用于过滤首字符空格和禁止输入标点符号与特殊字符
/// 同时支持输入法组词过程中的特殊字符
class NoSpecialCharsFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    // 如果文本为空，直接返回
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // 检查是否在输入法组词过程中（composing区域非空）
    // 如果正在组词，则允许特殊字符暂时存在
    if (newValue.composing.end > newValue.composing.start) {
      return newValue; // 输入法组词过程中不过滤
    }

    // 定义允许的字符正则表达式（字母、数字、中文和空格，但空格不能出现在首位和末尾）
    final RegExp allowedCharsRegex = RegExp(
      r'^[a-zA-Z0-9\u4e00-\u9fa5]+[a-zA-Z0-9\u4e00-\u9fa5 ]*[a-zA-Z0-9\u4e00-\u9fa5]+$|^[a-zA-Z0-9\u4e00-\u9fa5]+$',
    );

    // 检查是否符合规则
    if (allowedCharsRegex.hasMatch(newValue.text)) {
      return newValue;
    }

    // 如果不符合规则，处理文本
    String filteredText = newValue.text;

    // 如果首字符是空格，则移除首字符空格
    while (filteredText.isNotEmpty && filteredText[0] == ' ') {
      filteredText = filteredText.substring(1);
    }

    // 如果末尾字符是空格，则移除末尾空格
    while (filteredText.isNotEmpty && filteredText[filteredText.length - 1] == ' ') {
      filteredText = filteredText.substring(0, filteredText.length - 1);
    }

    // 移除所有不允许的特殊字符和标点符号
    filteredText = filteredText.replaceAll(RegExp(r'[^\w\s\u4e00-\u9fa5]'), '');

    // 计算新的光标位置
    int selectionOffset = newValue.selection.end;
    if (filteredText.length < newValue.text.length) {
      // 如果文本被过滤掉了字符，调整光标位置
      selectionOffset = filteredText.length;
      if (selectionOffset > oldValue.selection.end) {
        selectionOffset = oldValue.selection.end;
      }
    }

    // 保留允许的字符（字母、数字、中文和非首尾的空格）
    return TextEditingValue(
      text: filteredText,
      selection: TextSelection.collapsed(offset: selectionOffset),
    );
  }
}
